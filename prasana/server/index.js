const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

// Simple test route
app.get('/', (req, res) => {
  res.send('Backend server is running!');
});

// TODO: Add your routes here, e.g.:
// const routes = require('./src/routes');
// app.use('/api', routes);

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
}); 