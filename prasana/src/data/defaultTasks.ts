import { Task } from '../components/TaskList';

export const defaultTasks: Task[] = [
  { id: '', title: 'Client Approved', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Content', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 20 },
  { id: '', title: 'Images', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 10 },
  { id: '', title: 'Template', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Hero Page', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Navbar', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Main Page', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Footer', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'About Us', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Services', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 20 },
  { id: '', title: 'Review', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Domain Purchased', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
  { id: '', title: 'Hosting', description: '', priority: 'medium', status: 'todo', dueDate: '', assignee: null, progress: 5 },
]; 