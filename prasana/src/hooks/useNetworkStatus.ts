import { useState, useEffect } from 'react';

interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string;
}

/**
 * Hook to monitor network status and connection quality
 * Helps detect when user goes offline or has a slow connection
 */
export const useNetworkStatus = (): NetworkStatus => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSlowConnection, setIsSlowConnection] = useState(false);
  const [connectionType, setConnectionType] = useState('unknown');

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      console.log('🌐 Network: Back online');
    };

    const handleOffline = () => {
      setIsOnline(false);
      console.log('📵 Network: Gone offline');
    };

    // Check connection type and speed if available
    const checkConnectionQuality = () => {
      // @ts-ignore - navigator.connection is experimental
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      
      if (connection) {
        setConnectionType(connection.effectiveType || 'unknown');
        
        // Consider 2G or slow-2g as slow connections
        const slowTypes = ['slow-2g', '2g'];
        setIsSlowConnection(slowTypes.includes(connection.effectiveType));
        
        console.log(`🌐 Connection type: ${connection.effectiveType}, downlink: ${connection.downlink}Mbps`);
      }
    };

    // Initial check
    checkConnectionQuality();

    // Listen for online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen for connection changes if supported
    // @ts-ignore
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    if (connection) {
      connection.addEventListener('change', checkConnectionQuality);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (connection) {
        connection.removeEventListener('change', checkConnectionQuality);
      }
    };
  }, []);

  return {
    isOnline,
    isSlowConnection,
    connectionType
  };
};

/**
 * Hook to automatically retry operations when network comes back online
 */
export const useRetryOnOnline = (
  retryFn: () => void | Promise<void>,
  shouldRetry: boolean = true
) => {
  const { isOnline } = useNetworkStatus();
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    if (!isOnline) {
      setWasOffline(true);
    } else if (wasOffline && shouldRetry) {
      // Network came back online, retry the operation
      console.log('🔄 Network back online, retrying operation...');
      setWasOffline(false);
      
      // Small delay to ensure connection is stable
      setTimeout(() => {
        retryFn();
      }, 1000);
    }
  }, [isOnline, wasOffline, shouldRetry, retryFn]);

  return { isOnline, wasOffline };
};

export default useNetworkStatus;
