import { updateOrganizationalHierarchy, verifyHierarchy } from '../utils/updateOrgHierarchy';

// Run the organizational hierarchy update
async function runUpdate() {
  try {
    console.log('🚀 Starting organizational hierarchy update...');
    
    const result = await updateOrganizationalHierarchy();
    console.log('✅ Update completed:', result);
    
    console.log('\n🔍 Verifying updated hierarchy...');
    await verifyHierarchy();
    
  } catch (error) {
    console.error('❌ Update failed:', error);
  }
}

// Export for use in browser console or other scripts
(window as any).runOrgUpdate = runUpdate;

export { runUpdate };
