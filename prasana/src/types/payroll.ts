// Comprehensive Payroll Management Types

export interface EmployeeSalaryStructure {
  id: string;
  employee_id: string;
  basic_salary: number;
  hra: number;
  transport_allowance: number;
  medical_allowance: number;
  special_allowance: number;
  other_allowances: number;
  provident_fund: number;
  professional_tax: number;
  income_tax: number;
  other_deductions: number;
  effective_from: string;
  effective_to?: string;
  status: 'active' | 'inactive' | 'pending';
  approved_by?: string;
  approved_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface PayrollPeriod {
  id: string;
  period_name: string;
  start_date: string;
  end_date: string;
  status: 'draft' | 'processing' | 'completed' | 'locked';
  processed_by?: string;
  processed_at?: string;
  total_employees: number;
  total_gross_amount: number;
  total_deductions: number;
  total_net_amount: number;
  created_at: string;
  updated_at: string;
}

export interface PayrollTransaction {
  id: string;
  payroll_period_id: string;
  employee_id: string;
  basic_salary: number;
  hra: number;
  transport_allowance: number;
  medical_allowance: number;
  special_allowance: number;
  other_allowances: number;
  overtime_amount: number;
  bonus_amount: number;
  gross_salary: number;
  provident_fund: number;
  professional_tax: number;
  income_tax: number;
  other_deductions: number;
  loan_deductions: number;
  total_deductions: number;
  net_salary: number;
  working_days: number;
  present_days: number;
  leave_days: number;
  overtime_hours: number;
  status: 'draft' | 'approved' | 'paid' | 'cancelled';
  approved_by?: string;
  approved_at?: string;
  paid_at?: string;
  created_at: string;
  updated_at: string;
}

export interface TaxConfiguration {
  id: string;
  tax_type: string;
  tax_name: string;
  min_amount: number;
  max_amount?: number;
  tax_rate: number;
  calculation_method: 'percentage' | 'fixed' | 'slab';
  effective_from: string;
  effective_to?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SalaryRevisionHistory {
  id: string;
  employee_id: string;
  previous_salary?: number;
  new_salary: number;
  revision_type: 'increment' | 'promotion' | 'adjustment' | 'bonus';
  revision_percentage?: number;
  effective_date: string;
  reason?: string;
  approved_by?: string;
  approved_at?: string;
  status: 'pending' | 'approved' | 'rejected';
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface PayrollAuditLog {
  id: string;
  table_name: string;
  record_id: string;
  action: 'INSERT' | 'UPDATE' | 'DELETE';
  old_values?: any;
  new_values?: any;
  changed_by?: string;
  changed_at: string;
  ip_address?: string;
  user_agent?: string;
}

export interface EmployeeLoan {
  id: string;
  employee_id: string;
  loan_type: string;
  loan_amount: number;
  interest_rate: number;
  tenure_months: number;
  monthly_emi: number;
  outstanding_amount: number;
  start_date: string;
  end_date: string;
  status: 'active' | 'completed' | 'cancelled';
  approved_by?: string;
  approved_at?: string;
  created_at: string;
  updated_at: string;
}

export interface PayrollCalculationInput {
  employee_id: string;
  salary_structure: EmployeeSalaryStructure;
  attendance_data: {
    working_days: number;
    present_days: number;
    leave_days: number;
    overtime_hours: number;
  };
  loan_deductions: number;
  bonus_amount?: number;
}

export interface PayrollCalculationResult {
  gross_salary: number;
  total_deductions: number;
  net_salary: number;
  breakdown: {
    basic_salary: number;
    allowances: {
      hra: number;
      transport: number;
      medical: number;
      special: number;
      other: number;
    };
    overtime_amount: number;
    bonus_amount: number;
    deductions: {
      provident_fund: number;
      professional_tax: number;
      income_tax: number;
      loan_deductions: number;
      other_deductions: number;
    };
  };
}

export interface PayrollDashboardStats {
  current_period: PayrollPeriod | null;
  total_employees: number;
  processed_employees: number;
  pending_approvals: number;
  total_payroll_amount: number;
  average_salary: number;
  monthly_comparison: {
    current_month: number;
    previous_month: number;
    percentage_change: number;
  };
}

export interface SalarySlip {
  employee: {
    id: string;
    name: string;
    employee_code: string;
    designation: string;
    department: string;
    bank_account?: string;
    pan_number?: string;
  };
  payroll_period: PayrollPeriod;
  transaction: PayrollTransaction;
  calculation_breakdown: PayrollCalculationResult;
  year_to_date: {
    gross_salary: number;
    total_deductions: number;
    net_salary: number;
    tax_deducted: number;
  };
}

export interface PayrollReport {
  id: string;
  report_type: 'salary_register' | 'tax_summary' | 'pf_summary' | 'department_wise' | 'custom';
  title: string;
  period: PayrollPeriod;
  filters: {
    departments?: string[];
    employees?: string[];
    date_range?: {
      start: string;
      end: string;
    };
  };
  data: any[];
  generated_by: string;
  generated_at: string;
}

export interface PayrollProcessingStatus {
  period_id: string;
  total_employees: number;
  processed_count: number;
  failed_count: number;
  status: 'initializing' | 'processing' | 'completed' | 'failed';
  current_employee?: string;
  errors: Array<{
    employee_id: string;
    employee_name: string;
    error_message: string;
  }>;
  started_at: string;
  completed_at?: string;
}
