import { supabase } from '../supabaseClient';
import {
  Role,
  EmployeeRole,
  PermissionGroup,
  RoleTemplate,
  RoleApprovalRequest,
  RoleAuditLog,
  BulkRoleOperation,
  RoleFormData,
  BulkAssignmentData,
  RoleAssignmentFormData,
  RoleFilters,
  EmployeeRoleFilters,
  AuditLogFilters,
  RoleHierarchy,
  PermissionMatrix,
  RoleStatistics,
  PaginatedResponse
} from '../types/roleManagement';

class RoleManagementService {
  // Permission Groups
  async getPermissionGroups(): Promise<PermissionGroup[]> {
    const { data, error } = await supabase
      .from('permission_groups')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) throw error;
    return data || [];
  }

  // Role Templates
  async getRoleTemplates(category?: string): Promise<RoleTemplate[]> {
    let query = supabase
      .from('role_templates')
      .select('*')
      .eq('is_active', true);

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query.order('usage_count', { ascending: false });
    if (error) throw error;
    return data || [];
  }

  async createRoleFromTemplate(templateId: string, customizations: Partial<RoleFormData>): Promise<Role> {
    const template = await this.getRoleTemplate(templateId);
    if (!template) throw new Error('Template not found');

    const permissionGroups = await this.getPermissionGroups();
    const templatePermissions = template.permission_groups.reduce((acc: string[], groupId) => {
      const group = permissionGroups.find(g => g.id === groupId);
      return group ? [...acc, ...group.permissions] : acc;
    }, []);

    const roleData: RoleFormData = {
      name: customizations.name || template.name,
      description: customizations.description || template.description,
      permissions: [...templatePermissions, ...(template.permissions || [])],
      permission_groups: template.permission_groups,
      role_level: customizations.role_level || template.suggested_level,
      ...customizations
    };

    const role = await this.createRole(roleData);
    
    // Update template usage count
    await supabase
      .from('role_templates')
      .update({ usage_count: template.usage_count + 1 })
      .eq('id', templateId);

    return role;
  }

  private async getRoleTemplate(id: string): Promise<RoleTemplate | null> {
    const { data, error } = await supabase
      .from('role_templates')
      .select('*')
      .eq('id', id)
      .single();

    if (error) return null;
    return data;
  }

  // Enhanced Role Management
  async getRoles(filters?: RoleFilters): Promise<Role[]> {
    let query = supabase
      .from('roles')
      .select('*');

    if (filters) {
      if (filters.status === 'active') query = query.eq('is_active', true);
      if (filters.status === 'inactive') query = query.eq('is_active', false);
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.level) query = query.eq('role_level', filters.level);
      if (filters.category) query = query.eq('template_category', filters.category);
    }

    const { data, error } = await query.order('role_level').order('name');
    if (error) throw error;

    return data || [];
  }

  async getRoleHierarchy(): Promise<RoleHierarchy[]> {
    const roles = await this.getRoles({ status: 'active' } as RoleFilters);
    return this.buildRoleHierarchy(roles);
  }

  private buildRoleHierarchy(roles: Role[], parentId?: string, level = 0): RoleHierarchy[] {
    return roles
      .filter(role => role.parent_role_id === parentId)
      .map(role => ({
        role,
        level,
        children: this.buildRoleHierarchy(roles, role.id, level + 1),
        inherited_permissions: this.getInheritedPermissions(roles, role)
      }));
  }

  private getInheritedPermissions(roles: Role[], role: Role): string[] {
    const inherited = new Set<string>();
    let currentRole = role;

    while (currentRole.parent_role_id) {
      const parentRole = roles.find(r => r.id === currentRole.parent_role_id);
      if (parentRole) {
        parentRole.permissions.forEach(p => inherited.add(p));
        currentRole = parentRole;
      } else {
        break;
      }
    }

    return Array.from(inherited);
  }

  async createRole(roleData: RoleFormData, createdBy?: string): Promise<Role> {
    const { data, error } = await supabase
      .from('roles')
      .insert([{
        name: roleData.name,
        description: roleData.description,
        permissions: roleData.permissions,
        parent_role_id: roleData.parent_role_id,
        role_level: roleData.role_level,
        template_category: roleData.template_category,
        created_by: createdBy
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updateRole(roleId: string, updates: Partial<RoleFormData>): Promise<Role> {
    const { data, error } = await supabase
      .from('roles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', roleId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteRole(roleId: string): Promise<void> {
    // Check if role has assignments
    const { data: assignments } = await supabase
      .from('employee_roles')
      .select('id')
      .eq('role_id', roleId)
      .eq('is_active', true);

    if (assignments && assignments.length > 0) {
      throw new Error('Cannot delete role with active assignments');
    }

    const { error } = await supabase
      .from('roles')
      .update({ is_active: false })
      .eq('id', roleId);

    if (error) throw error;
  }

  // Enhanced Employee Role Management
  async getEmployeeRoles(filters?: EmployeeRoleFilters): Promise<EmployeeRole[]> {
    try {
      // First try with joins
      let query = supabase
        .from('employee_roles')
        .select(`
          *,
          role:roles(*),
          employee:employees(id, first_name, last_name, email, designation, department)
        `);

      if (filters) {
        if (filters.approval_status) query = query.eq('approval_status', filters.approval_status);
        if (filters.role_id) query = query.eq('role_id', filters.role_id);
        if (filters.expiry_status === 'expiring_soon') {
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
          query = query.lte('expiry_date', thirtyDaysFromNow.toISOString());
        }
        if (filters.expiry_status === 'expired') {
          query = query.lt('expiry_date', new Date().toISOString());
        }
      }

      const { data, error } = await query
        .eq('is_active', true)
        .order('assigned_date', { ascending: false });

      if (error) {
        // If join fails due to relationship conflicts, fall back to basic query
        console.warn('Join query failed, falling back to basic query:', error);
        return this.getEmployeeRolesBasic(filters);
      }

      return data || [];
    } catch (error) {
      console.warn('Employee roles query failed, using fallback:', error);
      return this.getEmployeeRolesBasic(filters);
    }
  }

  private async getEmployeeRolesBasic(filters?: EmployeeRoleFilters): Promise<any[]> {
    let query = supabase
      .from('employee_roles')
      .select('*');

    if (filters) {
      if (filters.approval_status) query = query.eq('approval_status', filters.approval_status);
      if (filters.role_id) query = query.eq('role_id', filters.role_id);
      // Skip search filter in basic mode as it requires joins
      if (filters.expiry_status === 'expiring_soon') {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        query = query.lte('expiry_date', thirtyDaysFromNow.toISOString());
      }
      if (filters.expiry_status === 'expired') {
        query = query.lt('expiry_date', new Date().toISOString());
      }
    }

    const { data: employeeRoles, error } = await query
      .eq('is_active', true)
      .order('assigned_date', { ascending: false });

    if (error) throw error;

    // Manually fetch related data
    const enrichedData = [];
    for (const er of employeeRoles || []) {
      try {
        // Fetch role data
        const { data: role } = await supabase
          .from('roles')
          .select('*')
          .eq('id', er.role_id)
          .single();

        // Fetch employee data
        const { data: employee } = await supabase
          .from('employees')
          .select('id, first_name, last_name, email, designation, department')
          .eq('id', er.employee_id)
          .single();

        enrichedData.push({
          ...er,
          role: role || { id: er.role_id, name: 'Unknown Role', permissions: [] },
          employee: employee || {
            id: er.employee_id,
            first_name: 'Unknown',
            last_name: 'Employee',
            email: '',
            designation: '',
            department: ''
          }
        });
      } catch (fetchError) {
        console.warn('Error fetching related data for employee role:', fetchError);
        // Add basic data even if fetch fails
        enrichedData.push({
          ...er,
          role: { id: er.role_id, name: 'Unknown Role', permissions: [] },
          employee: {
            id: er.employee_id,
            first_name: 'Unknown',
            last_name: 'Employee',
            email: '',
            designation: '',
            department: ''
          }
        });
      }
    }

    return enrichedData;
  }

  async assignRole(assignmentData: RoleAssignmentFormData, assignedBy: string): Promise<EmployeeRole> {
    const roleAssignment = {
      employee_id: assignmentData.employee_id,
      role_id: assignmentData.role_id,
      assigned_by: assignedBy,
      expiry_date: assignmentData.expiry_date,
      approval_status: assignmentData.require_approval ? 'pending' : 'approved'
    };

    if (assignmentData.require_approval) {
      // Create approval request
      await this.createApprovalRequest({
        employee_id: assignmentData.employee_id,
        role_id: assignmentData.role_id,
        requested_by: assignedBy,
        request_type: 'assign',
        justification: assignmentData.justification,
        priority: assignmentData.priority,
        expiry_date: assignmentData.expiry_date
      });

      throw new Error('Role assignment requires approval. Request has been submitted.');
    }

    const { data, error } = await supabase
      .from('employee_roles')
      .insert([roleAssignment])
      .select('*')
      .single();

    if (error) throw error;
    return data;
  }

  async bulkAssignRoles(bulkData: BulkAssignmentData, performedBy: string): Promise<BulkRoleOperation> {
    const operation = await this.createBulkOperation('bulk_assign', performedBy, bulkData.employee_ids.length, bulkData);

    try {
      let successful = 0;
      let failed = 0;

      for (const employeeId of bulkData.employee_ids) {
        try {
          await this.assignRole({
            employee_id: employeeId,
            role_id: bulkData.role_id,
            expiry_date: bulkData.expiry_date,
            justification: bulkData.justification,
            require_approval: bulkData.require_approval,
            priority: 'normal'
          }, performedBy);
          successful++;
        } catch (error) {
          failed++;
        }
      }

      await this.updateBulkOperation(operation.id, {
        successful_records: successful,
        failed_records: failed,
        status: 'completed'
      });

      return { ...operation, successful_records: successful, failed_records: failed, status: 'completed' };
    } catch (error) {
      await this.updateBulkOperation(operation.id, {
        status: 'failed',
        error_details: (error as Error).message
      });
      throw error;
    }
  }

  async removeRole(employeeRoleId: string, removedBy: string, reason?: string): Promise<void> {
    const { error } = await supabase
      .from('employee_roles')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', employeeRoleId);

    if (error) throw error;

    // Log the removal
    await this.logRoleAction(employeeRoleId, 'removed', removedBy, reason);
  }

  // Approval Workflow
  async createApprovalRequest(requestData: Partial<RoleApprovalRequest>): Promise<RoleApprovalRequest> {
    const { data, error } = await supabase
      .from('role_approval_requests')
      .insert([requestData])
      .select('*')
      .single();

    if (error) throw error;
    return data as any;
  }

  async getApprovalRequests(status?: string): Promise<RoleApprovalRequest[]> {
    let query = supabase
      .from('role_approval_requests')
      .select(`
        *,
        role:roles(*)
      `);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('requested_date', { ascending: false });
    if (error) throw error;
    return data || [];
  }

  async approveRequest(requestId: string, approverId: string, comments?: string): Promise<void> {
    const { data: request, error: fetchError } = await supabase
      .from('role_approval_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (fetchError) throw fetchError;

    // Update request status
    const { error: updateError } = await supabase
      .from('role_approval_requests')
      .update({
        status: 'approved',
        approver_id: approverId,
        approver_comments: comments,
        processed_date: new Date().toISOString()
      })
      .eq('id', requestId);

    if (updateError) throw updateError;

    // Execute the role assignment
    if (request.request_type === 'assign') {
      await supabase
        .from('employee_roles')
        .insert([{
          employee_id: request.employee_id,
          role_id: request.role_id,
          assigned_by: approverId,
          expiry_date: request.expiry_date,
          approval_status: 'approved',
          approved_by: approverId,
          approved_date: new Date().toISOString()
        }]);
    }
  }

  async rejectRequest(requestId: string, approverId: string, reason: string): Promise<void> {
    const { error } = await supabase
      .from('role_approval_requests')
      .update({
        status: 'rejected',
        approver_id: approverId,
        approver_comments: reason,
        processed_date: new Date().toISOString()
      })
      .eq('id', requestId);

    if (error) throw error;
  }

  // Audit and Logging
  async getAuditLogs(filters?: AuditLogFilters): Promise<RoleAuditLog[]> {
    let query = supabase
      .from('role_audit_log')
      .select(`
        *,
        role:roles(name)
      `);

    if (filters) {
      if (filters.action) query = query.eq('action', filters.action);
      if (filters.employee_id) query = query.eq('employee_id', filters.employee_id);
      if (filters.role_id) query = query.eq('role_id', filters.role_id);
      if (filters.date_from) query = query.gte('created_at', filters.date_from);
      if (filters.date_to) query = query.lte('created_at', filters.date_to);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(100);

    if (error) throw error;
    return data || [];
  }

  private async logRoleAction(
    employeeRoleId: string,
    action: string,
    performedBy: string,
    reason?: string,
    oldValues?: any,
    newValues?: any
  ): Promise<void> {
    await supabase
      .from('role_audit_log')
      .insert([{
        action,
        performed_by: performedBy,
        reason,
        old_values: oldValues,
        new_values: newValues
      }]);
  }

  // Bulk Operations
  private async createBulkOperation(
    operationType: string,
    performedBy: string,
    totalRecords: number,
    operationData: any
  ): Promise<BulkRoleOperation> {
    const { data, error } = await supabase
      .from('bulk_role_operations')
      .insert([{
        operation_type: operationType,
        performed_by: performedBy,
        total_records: totalRecords,
        operation_data: operationData
      }])
      .select('*')
      .single();

    if (error) throw error;
    return data as any;
  }

  private async updateBulkOperation(operationId: string, updates: any): Promise<void> {
    const { error } = await supabase
      .from('bulk_role_operations')
      .update({
        ...updates,
        completed_at: updates.status === 'completed' ? new Date().toISOString() : undefined
      })
      .eq('id', operationId);

    if (error) throw error;
  }

  // Statistics and Analytics
  async getRoleStatistics(): Promise<RoleStatistics> {
    try {
      const [rolesData, assignmentsData, approvalsData] = await Promise.all([
        supabase.from('roles').select('id, is_active'),
        supabase.from('employee_roles').select('role_id, is_active'),
        supabase.from('role_approval_requests').select('status')
      ]);

      const roles = rolesData.data || [];
      const assignments = assignmentsData.data || [];
      const approvals = approvalsData.data || [];

      return {
        total_roles: roles.length,
        active_roles: roles.filter(r => r.is_active).length,
        total_assignments: assignments.filter(a => a.is_active).length,
        pending_approvals: approvals.filter(a => a.status === 'pending').length,
        expiring_soon: 0, // TODO: Calculate based on expiry dates
        role_distribution: [], // Simplified for now
        permission_usage: [] // TODO: Calculate permission usage
      };
    } catch (error) {
      console.warn('Error getting role statistics:', error);
      return {
        total_roles: 0,
        active_roles: 0,
        total_assignments: 0,
        pending_approvals: 0,
        expiring_soon: 0,
        role_distribution: [],
        permission_usage: []
      };
    }
  }

  private calculateRoleDistribution(assignments: any[]): { role_name: string; assignment_count: number }[] {
    const distribution = assignments.reduce((acc, assignment) => {
      if (assignment.is_active && assignment.roles?.name) {
        acc[assignment.roles.name] = (acc[assignment.roles.name] || 0) + 1;
      }
      return acc;
    }, {});

    return Object.entries(distribution).map(([role_name, assignment_count]) => ({
      role_name,
      assignment_count: assignment_count as number
    }));
  }

  // Utility Methods
  async checkRoleExpiration(): Promise<void> {
    const { error } = await supabase.rpc('check_role_expiration');
    if (error) throw error;
  }

  async getPermissionMatrix(employeeIds: string[]): Promise<PermissionMatrix[]> {
    try {
      const { data, error } = await supabase
        .from('employee_roles')
        .select('employee_id, role_id, expiry_date')
        .in('employee_id', employeeIds)
        .eq('is_active', true);

      if (error) throw error;

      const matrix: PermissionMatrix[] = [];
      const employeeMap = new Map();

      // Simplified version without complex joins
      for (const assignment of data || []) {
        const empId = assignment.employee_id;
        if (!employeeMap.has(empId)) {
          employeeMap.set(empId, {
            employee_id: empId,
            employee_name: 'Employee', // Simplified
            roles: [],
            effective_permissions: new Set()
          });
        }

        const emp = employeeMap.get(empId);
        emp.roles.push({
          role_id: assignment.role_id,
          role_name: 'Role', // Simplified
          permissions: [],
          expiry_date: assignment.expiry_date
        });
      }

      employeeMap.forEach(emp => {
        matrix.push({
          ...emp,
          effective_permissions: Array.from(emp.effective_permissions)
        });
      });

      return matrix;
    } catch (error) {
      console.warn('Error getting permission matrix:', error);
      return [];
    }
  }
}

export const roleManagementService = new RoleManagementService();
