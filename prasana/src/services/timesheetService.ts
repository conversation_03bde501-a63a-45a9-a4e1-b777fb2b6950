import { supabase } from '../supabaseClient';
import { TimeEntry, TimeEntryForm, TimeEntrySummary } from '../types/timesheet';

// Helper function to get employee ID from user ID
const getEmployeeIdFromUserId = async (userId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('employees')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching employee ID:', error);
      return null;
    }

    return data?.id || null;
  } catch (error) {
    console.error('Error in getEmployeeIdFromUserId:', error);
    return null;
  }
};

export const timesheetService = {
    // Fetch time entries for the current user
    async getTimeEntries(startDate: string, endDate: string): Promise<TimeEntry[]> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                console.log('No authenticated user found');
                return [];
            }

            console.log('Fetching entries for user:', user.id, 'date range:', { startDate, endDate });

            // Get the employee ID for this user
            const employeeId = await getEmployeeIdFromUserId(user.id);
            if (!employeeId) {
                console.warn('Employee record not found for current user');
                return [];
            }

            console.log('Employee ID found:', employeeId);

            const { data, error } = await supabase
                .from('time_entries')
                .select(`
                    id,
                    employee_id,
                    user_id,
                    project_id,
                    project_name_text,
                    description,
                    start_time,
                    end_time,
                    duration,
                    entry_date,
                    date,
                    category,
                    tags,
                    created_at,
                    updated_at
                `)
                .eq('employee_id', employeeId)
                .gte('entry_date', startDate)
                .lte('entry_date', endDate)
                .order('entry_date', { ascending: false });

            if (error) {
                console.error('Supabase query error:', error);
                throw error;
            }

            console.log('Raw data from Supabase:', data);

            // Transform the data to match the TimeEntry interface
            const transformedEntries: TimeEntry[] = (data || []).map(entry => {
                if (!entry) {
                    console.warn('Null entry found in data');
                    return null;
                }
                
                if (!('id' in entry) || !('user_id' in entry) || !('date' in entry)) {
                     console.warn('Entry missing required minimal fields:', entry);
                     return null;
                }

                const transformed = {
                    id: entry.id,
                    employee_id: entry.employee_id,
                    user_id: entry.user_id,
                    project_id: entry.project_id,
                    projectName: entry.project_name_text || (entry.project_id ? `Project ID: ${entry.project_id}` : 'Unknown Project'),
                    project_name_text: entry.project_name_text,
                    description: entry.description,
                    start_time: entry.start_time,
                    end_time: entry.end_time,
                    duration: entry.duration,
                    date: entry.entry_date || entry.date, // Use entry_date if available, fallback to date
                    category: entry.category,
                    tags: entry.tags || [],
                    created_at: entry.created_at,
                    updated_at: entry.updated_at,
                } as TimeEntry;

                return transformed;
            }).filter((entry): entry is TimeEntry => entry !== null);

            console.log('Transformed entries:', transformedEntries);
            return transformedEntries;
        } catch (error) {
            console.error('Error in getTimeEntries:', error);
            return [];
        }
    },

    // Add a new time entry
    async addTimeEntry(entry: TimeEntryForm): Promise<TimeEntry> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) throw new Error('User not authenticated');

            console.log('Creating time entry for user:', user.id);

            // Get the employee ID for this user
            const employeeId = await getEmployeeIdFromUserId(user.id);
            if (!employeeId) {
                throw new Error('Employee record not found for current user. Please contact your administrator.');
            }

            console.log('Employee ID found:', employeeId);
            console.log('User-entered project name:', entry.projectId); // entry.projectId now holds the text name

            // --- Removed project lookup and creation logic ---
            // We will set project_id to null and save the text name.
            const projectIdToLink = null; // Always set project_id to null

            // Calculate start and end times based on duration
            const durationMinutes = entry.duration !== undefined ? entry.duration : (entry.hours * 60) + entry.minutes;
            const startTime = new Date(`${entry.date}T00:00:00Z`);
            const endTime = new Date(startTime.getTime() + durationMinutes * 60000); // Convert minutes to milliseconds

            // Format the data for Supabase
            const timeEntryData = {
                employee_id: employeeId, // Required field
                user_id: user.id, // Keep for backward compatibility
                project_id: projectIdToLink, // Explicitly set to null
                project_name_text: entry.projectId, // Save the user-entered text here
                description: entry.description,
                task_description: entry.description, // Map to the actual column name
                date: entry.date,
                entry_date: entry.date, // Map to the actual column name
                duration: durationMinutes,
                duration_minutes: durationMinutes, // Map to the actual column name
                category: entry.category || null,
                activity_type: entry.category || null, // Map to the actual column name
                tags: entry.tags || [],
                start_time: startTime.toISOString(),
                end_time: endTime.toISOString(),
                status: 'draft', // Set default status
                is_billable: true, // Set default billable status
                created_at: new Date().toISOString()
            };

            console.log('Sending time entry data to Supabase:', timeEntryData);

            const { data, error } = await supabase
                .from('time_entries')
                .insert([timeEntryData])
                .select(`
                    id,
                    user_id,
                    project_id,
                    project_name_text,
                    description,
                    start_time,
                    end_time,
                    duration,
                    date,
                    category,
                    tags,
                    created_at,
                    updated_at
                `)
                .single();

            if (error) {
                console.error('Supabase error:', error);
                throw new Error(`Failed to create time entry: ${error.message}`);
            }

            if (!data) {
                throw new Error('No data returned from Supabase');
            }

            // Transform the response to match TimeEntry interface
            const transformedEntry: TimeEntry = {
                id: data.id,
                user_id: data.user_id,
                project_id: data.project_id, // This will be null
                projectName: data.project_name_text || 'Unknown Project', // Use text name for frontend display
                project_name_text: data.project_name_text,
                description: data.description,
                start_time: data.start_time,
                end_time: data.end_time,
                duration: data.duration,
                date: data.date,
                category: data.category,
                tags: data.tags || [],
                created_at: data.created_at,
                updated_at: data.updated_at,
            };

            console.log('Time entry created successfully:', transformedEntry);
            return transformedEntry;
        } catch (error) {
            console.error('Error in addTimeEntry:', error);
            throw error;
        }
    },

    // Update an existing time entry
    async updateTimeEntry(id: string, entry: TimeEntryForm): Promise<TimeEntry> {
        try {
            const durationMinutes = (entry.hours * 60) + entry.minutes;
            const startTime = new Date(`${entry.date}T00:00:00Z`);
            const endTime = new Date(startTime.getTime() + durationMinutes * 60000);

            const { data, error } = await supabase
                .from('time_entries')
                .update({
                    project_id: entry.projectId,
                    description: entry.description,
                    start_time: startTime.toISOString(),
                    end_time: endTime.toISOString(),
                    duration: durationMinutes,
                    date: entry.date,
                    category: entry.category || null,
                    tags: entry.tags || []
                })
                .eq('id', id)
                .select(`
                    *,
                    projects (
                        name
                    )
                `)
                .single();

            if (error) throw error;

            return {
                id: data.id,
                projectId: data.project_id,
                projectName: data.projects?.name || 'Unknown Project',
                description: data.description,
                startTime: data.start_time,
                endTime: data.end_time,
                duration: data.duration,
                date: data.date,
                userId: data.user_id,
                category: data.category,
                tags: data.tags || []
            };
        } catch (error) {
            console.error('Error in updateTimeEntry:', error);
            throw error;
        }
    },

    // Delete a time entry
    async deleteTimeEntry(id: string): Promise<void> {
        try {
            const { error } = await supabase
                .from('time_entries')
                .delete()
                .eq('id', id);

            if (error) throw error;
        } catch (error) {
            console.error('Error in deleteTimeEntry:', error);
            throw error;
        }
    },

    // Get time entry summary
    async getTimeEntrySummary(startDate: string, endDate: string): Promise<TimeEntrySummary> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) throw new Error('User not authenticated');

            // Get the employee ID for this user
            const employeeId = await getEmployeeIdFromUserId(user.id);
            if (!employeeId) {
                throw new Error('Employee record not found for current user');
            }

            const { data, error } = await supabase
                .from('time_entries')
                .select('duration, duration_minutes, project_id, category, activity_type')
                .eq('employee_id', employeeId)
                .gte('entry_date', startDate)
                .lte('entry_date', endDate);

            if (error) throw error;

            const summary: TimeEntrySummary = {
                totalDuration: 0,
                entriesByProject: {},
                entriesByCategory: {}
            };

            data.forEach(entry => {
                const duration = entry.duration_minutes || entry.duration || 0;
                summary.totalDuration += duration;

                if (entry.project_id) {
                    summary.entriesByProject[entry.project_id] =
                        (summary.entriesByProject[entry.project_id] || 0) + duration;
                }

                const category = entry.activity_type || entry.category;
                if (category) {
                    summary.entriesByCategory[category] =
                        (summary.entriesByCategory[category] || 0) + duration;
                }
            });

            return summary;
        } catch (error) {
            console.error('Error in getTimeEntrySummary:', error);
            throw error;
        }
    },

    // Fetch all time entries for all users
    async getAllTimeEntries(startDate: string, endDate: string): Promise<TimeEntry[]> {
        try {
            console.log('getAllTimeEntries called with date range:', { startDate, endDate });

            // Query for entries with entry_date
            const { data: entriesWithEntryDate, error: error1 } = await supabase
                .from('time_entries')
                .select(`
                    id,
                    employee_id,
                    user_id,
                    project_id,
                    project_name_text,
                    description,
                    start_time,
                    end_time,
                    duration,
                    duration_minutes,
                    entry_date,
                    date,
                    category,
                    activity_type,
                    tags,
                    created_at,
                    updated_at
                `)
                .not('entry_date', 'is', null)
                .gte('entry_date', startDate)
                .lte('entry_date', endDate);

            // Query for entries with only date field (legacy)
            const { data: entriesWithDate, error: error2 } = await supabase
                .from('time_entries')
                .select(`
                    id,
                    employee_id,
                    user_id,
                    project_id,
                    project_name_text,
                    description,
                    start_time,
                    end_time,
                    duration,
                    duration_minutes,
                    entry_date,
                    date,
                    category,
                    activity_type,
                    tags,
                    created_at,
                    updated_at
                `)
                .is('entry_date', null)
                .not('date', 'is', null)
                .gte('date', startDate)
                .lte('date', endDate);

            if (error1) {
                console.error('Error fetching entries with entry_date:', error1);
            }
            if (error2) {
                console.error('Error fetching entries with date:', error2);
            }

            // Combine both result sets
            const allData = [
                ...(entriesWithEntryDate || []),
                ...(entriesWithDate || [])
            ];

            console.log('Raw data from getAllTimeEntries:', allData);

            const transformedEntries: TimeEntry[] = (allData || []).map(entry => {
                if (!entry) return undefined;
                if (!('id' in entry)) return undefined;

                const result: TimeEntry = {
                    id: entry.id,
                    employee_id: entry.employee_id,
                    user_id: entry.user_id,
                    project_id: entry.project_id,
                    projectName: entry.project_name_text || (entry.project_id ? `Project ID: ${entry.project_id}` : 'Unknown Project'),
                    description: entry.description,
                    start_time: entry.start_time,
                    end_time: entry.end_time,
                    duration: entry.duration_minutes || entry.duration || 0, // Use duration_minutes first
                    date: entry.entry_date || entry.date, // Use entry_date first
                    created_at: entry.created_at,
                    updated_at: entry.updated_at,
                };

                if (entry.project_name_text) result.project_name_text = entry.project_name_text;
                if (entry.activity_type || entry.category) result.category = entry.activity_type || entry.category;
                if (entry.tags) result.tags = entry.tags;

                return result;
            }).filter((entry): entry is TimeEntry => !!entry);

            // Sort by date (newest first)
            transformedEntries.sort((a, b) => {
                const dateA = new Date(a.date);
                const dateB = new Date(b.date);
                return dateB.getTime() - dateA.getTime();
            });

            console.log('Transformed entries from getAllTimeEntries:', transformedEntries);
            console.log('Total entries found:', transformedEntries.length);

            return transformedEntries;
        } catch (error) {
            console.error('Error in getAllTimeEntries:', error);
            return [];
        }
    }
}; 