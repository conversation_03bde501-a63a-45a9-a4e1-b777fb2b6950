import { supabase } from '../supabaseClient';
import { validateProfileUpdate, sanitizeProfileUpdate, validateProfilePicture } from '../utils/profileValidation';
import { logProfileUpdate, logProfilePictureUpload, checkRateLimit, logSecurityEvent } from './auditService';

// Enhanced profile interface that combines user and employee data
export interface UserProfile {
  // User table fields
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  phone?: string;
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;

  // Employee table fields (if employee record exists)
  employee_id?: string;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  gender?: string;
  marital_status?: string;
  nationality?: string;
  designation?: string;
  department?: string;
  team_id?: string;
  team_name?: string;
  location?: string;
  manager_id?: string;
  manager_name?: string;
  employment_type?: string;
  joining_date?: string;
  probation_end_date?: string;
  personal_email?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  current_address?: string;
  permanent_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  status?: string;
  termination_date?: string;
  termination_reason?: string;
  casual_leave_balance?: number;
  sick_leave_balance?: number;
  annual_leave_balance?: number;
  profile_picture?: string;
  employee_code?: string;
  hire_date?: string;
  reports_to?: string;
  skills?: string[];
  bio?: string;
}

export interface ProfileUpdateData {
  // Basic user info
  full_name?: string;
  phone?: string;
  avatar_url?: string;

  // Personal details
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  gender?: string;
  marital_status?: string;
  nationality?: string;
  personal_email?: string;
  bio?: string;

  // Contact information
  current_address?: string;
  permanent_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;

  // Emergency contact
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;

  // Skills
  skills?: string[];
}

/**
 * Fetch complete user profile including employee data
 */
export const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    // Use the database function for optimized data retrieval
    const { data, error } = await supabase.rpc('get_user_profile', {
      user_uuid: userId
    });

    if (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      return null;
    }

    // Return the first (and should be only) result
    return data[0] as UserProfile;
  } catch (error) {
    console.error('Error in fetchUserProfile:', error);
    throw error;
  }
};

/**
 * Update user profile with data synchronization between users and employees tables
 */
export const updateUserProfile = async (
  userId: string,
  updateData: ProfileUpdateData
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Rate limiting check
    const rateLimit = await checkRateLimit(userId, 'UPDATE', 60, 10);
    if (!rateLimit.allowed) {
      await logSecurityEvent(userId, 'RATE_LIMIT_EXCEEDED', {
        action: 'profile_update',
        remaining_attempts: rateLimit.remainingAttempts
      });
      return { success: false, error: 'Too many update attempts. Please try again later.' };
    }

    // Validate input data
    const validation = validateProfileUpdate(updateData);
    if (!validation.isValid) {
      await logSecurityEvent(userId, 'INVALID_DATA', {
        action: 'profile_update',
        validation_errors: validation.errors
      });
      return {
        success: false,
        error: validation.errors.map(e => e.message).join(', ')
      };
    }

    // Sanitize input data
    const sanitizedData = sanitizeProfileUpdate(updateData);

    // Get current data for audit logging
    const currentProfile = await fetchUserProfile(userId);

    // Start a transaction-like operation
    const updates: Promise<any>[] = [];

    // Prepare user table updates
    const userUpdates: any = {};
    if (sanitizedData.full_name !== undefined) userUpdates.full_name = sanitizedData.full_name;
    if (sanitizedData.phone !== undefined) userUpdates.phone = sanitizedData.phone;
    if (sanitizedData.avatar_url !== undefined) userUpdates.avatar_url = sanitizedData.avatar_url;

    // Always update the updated_at timestamp
    userUpdates.updated_at = new Date().toISOString();

    // Update users table if there are changes
    if (Object.keys(userUpdates).length > 1) { // More than just updated_at
      updates.push(
        supabase
          .from('users')
          .update(userUpdates)
          .eq('id', userId)
      );
    }

    // Prepare employee table updates
    const employeeUpdates: any = {};
    if (sanitizedData.first_name !== undefined) employeeUpdates.first_name = sanitizedData.first_name;
    if (sanitizedData.last_name !== undefined) employeeUpdates.last_name = sanitizedData.last_name;
    if (sanitizedData.date_of_birth !== undefined) employeeUpdates.date_of_birth = sanitizedData.date_of_birth;
    if (sanitizedData.gender !== undefined) employeeUpdates.gender = sanitizedData.gender;
    if (sanitizedData.marital_status !== undefined) employeeUpdates.marital_status = sanitizedData.marital_status;
    if (sanitizedData.nationality !== undefined) employeeUpdates.nationality = sanitizedData.nationality;
    if (sanitizedData.personal_email !== undefined) employeeUpdates.personal_email = sanitizedData.personal_email;
    if (sanitizedData.bio !== undefined) employeeUpdates.bio = sanitizedData.bio;
    if (sanitizedData.current_address !== undefined) employeeUpdates.current_address = sanitizedData.current_address;
    if (sanitizedData.permanent_address !== undefined) employeeUpdates.permanent_address = sanitizedData.permanent_address;
    if (sanitizedData.city !== undefined) employeeUpdates.city = sanitizedData.city;
    if (sanitizedData.state !== undefined) employeeUpdates.state = sanitizedData.state;
    if (sanitizedData.postal_code !== undefined) employeeUpdates.postal_code = sanitizedData.postal_code;
    if (sanitizedData.country !== undefined) employeeUpdates.country = sanitizedData.country;
    if (sanitizedData.emergency_contact_name !== undefined) employeeUpdates.emergency_contact_name = sanitizedData.emergency_contact_name;
    if (sanitizedData.emergency_contact_phone !== undefined) employeeUpdates.emergency_contact_phone = sanitizedData.emergency_contact_phone;
    if (sanitizedData.emergency_contact_relationship !== undefined) employeeUpdates.emergency_contact_relationship = sanitizedData.emergency_contact_relationship;
    if (sanitizedData.skills !== undefined) employeeUpdates.skills = sanitizedData.skills;
    if (sanitizedData.phone !== undefined) employeeUpdates.phone = sanitizedData.phone; // Sync phone to employee table too

    // Always update the updated_at timestamp
    employeeUpdates.updated_at = new Date().toISOString();

    // Update employee table if there are changes and employee record exists
    if (Object.keys(employeeUpdates).length > 1) { // More than just updated_at
      // Check if employee record exists first
      const { data: existingEmployee } = await supabase
        .from('employees')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (existingEmployee) {
        updates.push(
          supabase
            .from('employees')
            .update(employeeUpdates)
            .eq('user_id', userId)
        );
      }
    }

    // Execute all updates
    const results = await Promise.all(updates);

    // Check for errors
    for (const result of results) {
      if (result.error) {
        console.error('Error updating profile:', result.error);
        await logSecurityEvent(userId, 'SUSPICIOUS_ACTIVITY', {
          action: 'profile_update_failed',
          error: result.error.message
        });
        return { success: false, error: result.error.message };
      }
    }

    // Log successful updates
    if (Object.keys(userUpdates).length > 1) { // More than just updated_at
      await logProfileUpdate(
        userId,
        'UPDATE',
        'users',
        userId,
        currentProfile ? {
          full_name: currentProfile.full_name,
          phone: currentProfile.phone,
          avatar_url: currentProfile.avatar_url
        } : undefined,
        userUpdates
      );
    }

    if (Object.keys(employeeUpdates).length > 1) { // More than just updated_at
      await logProfileUpdate(
        userId,
        'UPDATE',
        'employees',
        userId,
        currentProfile ? {
          first_name: currentProfile.first_name,
          last_name: currentProfile.last_name,
          // ... other relevant fields
        } : undefined,
        employeeUpdates
      );
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in updateUserProfile:', error);
    return { success: false, error: error.message || 'Failed to update profile' };
  }
};

/**
 * Upload profile picture to Supabase Storage
 */
export const uploadProfilePicture = async (
  userId: string,
  file: File
): Promise<{ success: boolean; url?: string; error?: string }> => {
  try {
    // Rate limiting check
    const rateLimit = await checkRateLimit(userId, 'UPLOAD_SUCCESS', 60, 5);
    if (!rateLimit.allowed) {
      await logSecurityEvent(userId, 'RATE_LIMIT_EXCEEDED', {
        action: 'profile_picture_upload',
        remaining_attempts: rateLimit.remainingAttempts
      });
      return { success: false, error: 'Too many upload attempts. Please try again later.' };
    }

    // Validate file
    const validation = validateProfilePicture(file);
    if (!validation.isValid) {
      await logProfilePictureUpload(
        userId,
        file.name,
        file.size,
        false,
        validation.errors.map(e => e.message).join(', ')
      );
      return {
        success: false,
        error: validation.errors.map(e => e.message).join(', ')
      };
    }

    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}-${Date.now()}.${fileExt}`;
    const filePath = `profile-pictures/${fileName}`;

    // Upload file to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      await logProfilePictureUpload(userId, file.name, file.size, false, uploadError.message);
      return { success: false, error: uploadError.message };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(filePath);

    const publicUrl = urlData.publicUrl;

    // Update both users and employees tables with new avatar URL
    const updates: Promise<any>[] = [];

    // Update users table
    updates.push(
      supabase
        .from('users')
        .update({
          avatar_url: publicUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
    );

    // Update employees table if employee record exists
    const { data: existingEmployee } = await supabase
      .from('employees')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingEmployee) {
      updates.push(
        supabase
          .from('employees')
          .update({
            profile_picture: publicUrl,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
      );
    }

    // Execute all updates
    const results = await Promise.all(updates);

    // Check for errors
    for (const result of results) {
      if (result.error) {
        console.error('Error updating profile picture:', result.error);
        await logProfilePictureUpload(userId, file.name, file.size, false, result.error.message);
        return { success: false, error: result.error.message };
      }
    }

    // Log successful upload
    await logProfilePictureUpload(userId, file.name, file.size, true);

    return { success: true, url: publicUrl };
  } catch (error: any) {
    console.error('Error in uploadProfilePicture:', error);
    return { success: false, error: error.message || 'Failed to upload profile picture' };
  }
};
