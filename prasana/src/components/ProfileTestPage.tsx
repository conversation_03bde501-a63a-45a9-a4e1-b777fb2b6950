import React, { useState } from 'react';
import { useUser } from '../contexts/UserContext';
import { fetchUserProfile, updateUserProfile } from '../services/profileService';
import { UserProfile } from '../services/profileService';

const ProfileTestPage: React.FC = () => {
  const { currentUser } = useUser();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<string>('');

  const handleFetchProfile = async () => {
    if (!currentUser?.id) {
      setMessage('No user logged in');
      return;
    }

    try {
      setLoading(true);
      setMessage('Fetching profile...');
      
      const profileData = await fetchUserProfile(currentUser.id);
      setProfile(profileData);
      setMessage('Profile fetched successfully!');
    } catch (error: any) {
      setMessage(`Error fetching profile: ${error.message}`);
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTestUpdate = async () => {
    if (!currentUser?.id) {
      setMessage('No user logged in');
      return;
    }

    try {
      setLoading(true);
      setMessage('Testing profile update...');
      
      const testData = {
        bio: `Test update at ${new Date().toISOString()}`
      };

      const result = await updateUserProfile(currentUser.id, testData);
      
      if (result.success) {
        setMessage('Profile update test successful!');
        // Refresh profile data
        await handleFetchProfile();
      } else {
        setMessage(`Profile update failed: ${result.error}`);
      }
    } catch (error: any) {
      setMessage(`Error updating profile: ${error.message}`);
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Profile Management Test Page</h1>
      
      {/* Current User Info */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Current User</h2>
        {currentUser ? (
          <div>
            <p><strong>ID:</strong> {currentUser.id}</p>
            <p><strong>Name:</strong> {currentUser.name}</p>
            <p><strong>Email:</strong> {currentUser.email}</p>
            <p><strong>Is Admin:</strong> {currentUser.isAdmin ? 'Yes' : 'No'}</p>
            <p><strong>Is Super Admin:</strong> {currentUser.isSuperAdmin ? 'Yes' : 'No'}</p>
          </div>
        ) : (
          <p>No user logged in</p>
        )}
      </div>

      {/* Test Controls */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-lg font-semibold mb-4">Test Controls</h2>
        <div className="space-x-4">
          <button
            onClick={handleFetchProfile}
            disabled={loading || !currentUser}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Fetch Profile'}
          </button>
          <button
            onClick={handleTestUpdate}
            disabled={loading || !currentUser}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Test Update'}
          </button>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.includes('Error') || message.includes('failed') 
            ? 'bg-red-50 text-red-800 border border-red-200' 
            : 'bg-green-50 text-green-800 border border-green-200'
        }`}>
          {message}
        </div>
      )}

      {/* Profile Data Display */}
      {profile && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Profile Data</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Basic Information</h3>
              <div className="space-y-1 text-sm">
                <p><strong>ID:</strong> {profile.id}</p>
                <p><strong>Full Name:</strong> {profile.full_name || 'Not set'}</p>
                <p><strong>Email:</strong> {profile.email}</p>
                <p><strong>Phone:</strong> {profile.phone || 'Not set'}</p>
                <p><strong>Active:</strong> {profile.is_active ? 'Yes' : 'No'}</p>
              </div>
            </div>
            
            {profile.employee_id && (
              <div>
                <h3 className="font-medium text-gray-700 mb-2">Employee Information</h3>
                <div className="space-y-1 text-sm">
                  <p><strong>PS Number:</strong> {profile.employee_id}</p>
                  <p><strong>First Name:</strong> {profile.first_name || 'Not set'}</p>
                  <p><strong>Last Name:</strong> {profile.last_name || 'Not set'}</p>
                  <p><strong>Designation:</strong> {profile.designation || 'Not set'}</p>
                  <p><strong>Department:</strong> {profile.department || 'Not set'}</p>
                  <p><strong>Location:</strong> {profile.location || 'Not set'}</p>
                </div>
              </div>
            )}
            
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Personal Details</h3>
              <div className="space-y-1 text-sm">
                <p><strong>Date of Birth:</strong> {profile.date_of_birth || 'Not set'}</p>
                <p><strong>Gender:</strong> {profile.gender || 'Not set'}</p>
                <p><strong>Nationality:</strong> {profile.nationality || 'Not set'}</p>
                <p><strong>Marital Status:</strong> {profile.marital_status || 'Not set'}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Contact Information</h3>
              <div className="space-y-1 text-sm">
                <p><strong>Personal Email:</strong> {profile.personal_email || 'Not set'}</p>
                <p><strong>Current Address:</strong> {profile.current_address || 'Not set'}</p>
                <p><strong>City:</strong> {profile.city || 'Not set'}</p>
                <p><strong>State:</strong> {profile.state || 'Not set'}</p>
                <p><strong>Country:</strong> {profile.country || 'Not set'}</p>
              </div>
            </div>
          </div>
          
          {profile.bio && (
            <div className="mt-4">
              <h3 className="font-medium text-gray-700 mb-2">Bio</h3>
              <p className="text-sm bg-gray-50 p-3 rounded">{profile.bio}</p>
            </div>
          )}
          
          {profile.skills && profile.skills.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium text-gray-700 mb-2">Skills</h3>
              <div className="flex flex-wrap gap-2">
                {profile.skills.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h3 className="font-medium text-gray-700 mb-2">Timestamps</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <p><strong>Created:</strong> {new Date(profile.created_at).toLocaleString()}</p>
              <p><strong>Updated:</strong> {new Date(profile.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileTestPage;
