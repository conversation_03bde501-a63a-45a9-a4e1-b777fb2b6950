import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Search,
  Filter,
  DollarSign,
  TrendingUp,
  Calendar,
  User,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { EmployeeSalaryStructure, SalaryRevisionHistory } from '../../types/payroll';
import { payrollService } from '../../services/payrollService';
import { formatINR, formatEmployeeName, formatDepartment, formatDesignation } from '../../utils/currency';
import { supabase } from '../../supabaseClient';

interface Employee {
  id: string;
  first_name: string;
  last_name: string;
  designation: string;
  department: string;
  employee_code: string;
  status: string;
  salary_structure?: EmployeeSalaryStructure;
}

const SalaryManagement: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [salaryStructures, setSalaryStructures] = useState<EmployeeSalaryStructure[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [showSalaryForm, setShowSalaryForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadEmployeesAndSalaries();
  }, []);

  const loadEmployeesAndSalaries = async () => {
    try {
      setLoading(true);

      // Fetch real employees from database
      const { data: employeesData, error: empError } = await supabase
        .from('employees')
        .select('*')
        .eq('status', 'active')
        .order('first_name');

      if (empError) throw empError;

      // Fetch salary structures for each employee
      const employeesWithSalary: Employee[] = [];

      for (const emp of employeesData || []) {
        const salaryStructure = await payrollService.getEmployeeSalaryStructure(emp.id);
        employeesWithSalary.push({
          ...emp,
          salary_structure: salaryStructure
        });
      }

      setEmployees(employeesWithSalary);
    } catch (error) {
      console.error('Error loading employees and salaries:', error);
      setEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredEmployees = employees.filter(emp =>
    formatEmployeeName(emp.first_name, emp.last_name).toLowerCase().includes(searchTerm.toLowerCase()) ||
    (emp.employee_code || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (emp.designation || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (emp.department || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateSalaryStructure = (employee: Employee) => {
    setSelectedEmployee(employee);
    setShowSalaryForm(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Salary Management</h1>
          <p className="text-gray-600 mt-1">Manage employee salary structures and revisions</p>
        </div>
        <button
          onClick={() => setShowSalaryForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Add Salary Structure
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search employees..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
          <Filter className="h-4 w-4" />
          Filters
        </button>
      </div>

      {/* Employee Salary List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Employee Salary Structures</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Salary
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Revision
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEmployees.map((employee) => (
                <tr key={employee.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {formatEmployeeName(employee.first_name, employee.last_name)}
                        </div>
                        <div className="text-sm text-gray-500">{employee.employee_code || 'N/A'}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{formatDepartment(employee.department)}</div>
                    <div className="text-sm text-gray-500">{formatDesignation(employee.designation)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {employee.salary_structure ? (
                      <>
                        <div className="text-sm font-medium text-gray-900">
                          {formatINR(
                            employee.salary_structure.basic_salary +
                            employee.salary_structure.hra +
                            employee.salary_structure.transport_allowance +
                            employee.salary_structure.medical_allowance +
                            employee.salary_structure.special_allowance +
                            employee.salary_structure.other_allowances,
                            { showDecimals: false }
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          Basic: {formatINR(employee.salary_structure.basic_salary, { showDecimals: false })}
                        </div>
                      </>
                    ) : (
                      <div className="text-sm text-gray-500">Not configured</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {employee.salary_structure ? (
                      <>
                        <div className="text-sm text-gray-900">
                          {new Date(employee.salary_structure.effective_from).toLocaleDateString('en-IN', {
                            month: 'short',
                            year: 'numeric'
                          })}
                        </div>
                        <div className="text-sm text-gray-500">Current structure</div>
                      </>
                    ) : (
                      <div className="text-sm text-gray-500">No history</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      employee.salary_structure?.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : employee.salary_structure?.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {employee.salary_structure?.status === 'active' && <CheckCircle className="h-3 w-3 mr-1" />}
                      {employee.salary_structure?.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                      {!employee.salary_structure && <AlertCircle className="h-3 w-3 mr-1" />}
                      {employee.salary_structure?.status || 'Not Set'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleCreateSalaryStructure(employee)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900">
                        <TrendingUp className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Salary Revisions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Salary Revisions</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="p-2 bg-green-100 rounded-full">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">John Doe - Software Engineer</p>
                <p className="text-sm text-gray-600">Salary increased from ₹75,000 to ₹85,000 (13.3% increment)</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-green-600">Approved</p>
                <p className="text-xs text-gray-500">Dec 1, 2024</p>
              </div>
            </div>

            <div className="flex items-center gap-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="p-2 bg-yellow-100 rounded-full">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Jane Smith - Product Manager</p>
                <p className="text-sm text-gray-600">Promotion increment from ₹95,000 to ₹110,000 (15.8% increment)</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-yellow-600">Pending</p>
                <p className="text-xs text-gray-500">Dec 5, 2024</p>
              </div>
            </div>

            <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="p-2 bg-blue-100 rounded-full">
                <DollarSign className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Mike Johnson - Designer</p>
                <p className="text-sm text-gray-600">Annual increment from ₹65,000 to ₹72,000 (10.8% increment)</p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-blue-600">Scheduled</p>
                <p className="text-xs text-gray-500">Jan 1, 2025</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Salary Form Modal */}
      {showSalaryForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {selectedEmployee ? `Edit Salary - ${selectedEmployee.first_name} ${selectedEmployee.last_name}` : 'Add Salary Structure'}
              </h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Basic Salary</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="50000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">HRA</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="20000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Transport Allowance</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="5000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Medical Allowance</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="3000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Special Allowance</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="7000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Effective From</label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => setShowSalaryForm(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Save Salary Structure
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalaryManagement;
