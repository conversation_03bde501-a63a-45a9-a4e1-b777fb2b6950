import React, { useState } from 'react';
import {
  BarChart3,
  DollarSign,
  FileText,
  Settings,
  Calendar,
  Users,
  TrendingUp,
  Shield
} from 'lucide-react';
import PayrollDashboard from './PayrollDashboard';
import SalaryManagement from './SalaryManagement';
import PayrollReports from './PayrollReports';

type PayrollTab = 'dashboard' | 'salary' | 'reports' | 'settings';

const PayrollManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<PayrollTab>('dashboard');

  const tabs = [
    {
      id: 'dashboard' as PayrollTab,
      name: 'Dashboard',
      icon: BarChart3,
      description: 'Overview and statistics'
    },
    {
      id: 'salary' as PayrollTab,
      name: 'Salary Management',
      icon: DollarSign,
      description: 'Manage employee salaries'
    },
    {
      id: 'reports' as PayrollTab,
      name: 'Reports',
      icon: FileText,
      description: 'Generate payroll reports'
    },
    {
      id: 'settings' as PayrollTab,
      name: 'Settings',
      icon: Settings,
      description: 'Configure payroll settings'
    }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <PayrollDashboard />;
      case 'salary':
        return <SalaryManagement />;
      case 'reports':
        return <PayrollReports />;
      case 'settings':
        return <PayrollSettings />;
      default:
        return <PayrollDashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Super Admin Access Notice */}
      <div className="bg-gradient-to-r from-red-500 to-red-600 text-white p-4">
        <div className="max-w-7xl mx-auto flex items-center gap-3">
          <Shield className="h-5 w-5" />
          <div>
            <p className="font-semibold">Super Admin Access Required</p>
            <p className="text-sm text-red-100">
              This section contains sensitive payroll information and is restricted to Super Administrators only.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <IconComponent
                      className={`-ml-0.5 mr-2 h-5 w-5 ${
                        activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                      }`}
                    />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[600px]">
          <div className="p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

// Payroll Settings Component
const PayrollSettings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Payroll Settings</h1>
        <p className="text-gray-600 mt-1">Configure payroll processing parameters and tax settings</p>
      </div>

      {/* Tax Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tax Configuration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Professional Tax Rate (%)</label>
            <input
              type="number"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="2.00"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">PF Contribution Rate (%)</label>
            <input
              type="number"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="12.00"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">ESI Rate (%)</label>
            <input
              type="number"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.75"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">TDS Rate (%)</label>
            <input
              type="number"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="10.00"
            />
          </div>
        </div>
      </div>

      {/* Payroll Processing Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Payroll Processing Settings</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">Auto-process Payroll</p>
              <p className="text-sm text-gray-600">Automatically process payroll on the last day of each month</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">Email Salary Slips</p>
              <p className="text-sm text-gray-600">Automatically email salary slips to employees</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">Require Approval</p>
              <p className="text-sm text-gray-600">Require super admin approval before processing payroll</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Company Information */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Company PAN</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="**********"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">TAN Number</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="ABCD12345E"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">PF Registration Number</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="KA/BGE/12345/000/000000"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">ESI Registration Number</label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="12345678901234567890"
            />
          </div>
        </div>
      </div>

      {/* Save Settings */}
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Save Settings
        </button>
      </div>
    </div>
  );
};

export default PayrollManagement;
