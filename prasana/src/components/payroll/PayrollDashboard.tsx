import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  Users,
  Calendar,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Download,
  Play,
  Settings,
  BarChart3
} from 'lucide-react';
import { PayrollDashboardStats, PayrollPeriod } from '../../types/payroll';
import { payrollService } from '../../services/payrollService';
import { formatINR, formatINRCompact, formatPercentage, formatPeriodName } from '../../utils/currency';

const PayrollDashboard: React.FC = () => {
  const [stats, setStats] = useState<PayrollDashboardStats | null>(null);
  const [currentPeriod, setCurrentPeriod] = useState<PayrollPeriod | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load current payroll period
      const period = await payrollService.getCurrentPayrollPeriod();
      setCurrentPeriod(period);

      // Load real dashboard statistics
      const dashboardStats = await payrollService.getDashboardStats();
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Fallback to basic stats if API fails
      const fallbackStats: PayrollDashboardStats = {
        current_period: currentPeriod,
        total_employees: 0,
        processed_employees: 0,
        pending_approvals: 0,
        total_payroll_amount: 0,
        average_salary: 0,
        monthly_comparison: {
          current_month: 0,
          previous_month: 0,
          percentage_change: 0
        }
      };
      setStats(fallbackStats);
    } finally {
      setLoading(false);
    }
  };

  const handleProcessPayroll = async () => {
    if (!currentPeriod) return;
    
    try {
      setProcessing(true);
      const result = await payrollService.processPayrollForPeriod(currentPeriod.id);
      console.log('Payroll processing result:', result);
      // Refresh dashboard data
      await loadDashboardData();
    } catch (error) {
      console.error('Error processing payroll:', error);
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-black gradient-text header-section animate-scaleIn">Payroll Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage and monitor payroll operations</p>
        </div>
        <div className="flex gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
            <Settings className="h-4 w-4" />
            Settings
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Download className="h-4 w-4" />
            Export Reports
          </button>
        </div>
      </div>

      {/* Current Period Status */}
      {currentPeriod && (
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold mb-2">Current Payroll Period</h3>
              <p className="text-blue-100">{formatPeriodName(currentPeriod.start_date, currentPeriod.end_date)}</p>
              <p className="text-blue-100 text-sm">
                {new Date(currentPeriod.start_date).toLocaleDateString('en-IN')} - {new Date(currentPeriod.end_date).toLocaleDateString('en-IN')}
              </p>
            </div>
            <div className="text-right">
              <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                currentPeriod.status === 'completed' ? 'bg-green-500/20 text-green-100' :
                currentPeriod.status === 'processing' ? 'bg-yellow-500/20 text-yellow-100' :
                'bg-blue-500/20 text-blue-100'
              }`}>
                {currentPeriod.status === 'completed' ? <CheckCircle className="h-4 w-4" /> :
                 currentPeriod.status === 'processing' ? <Clock className="h-4 w-4" /> :
                 <AlertCircle className="h-4 w-4" />}
                {currentPeriod.status.charAt(0).toUpperCase() + currentPeriod.status.slice(1)}
              </div>
              {currentPeriod.status === 'draft' && (
                <button
                  onClick={handleProcessPayroll}
                  disabled={processing}
                  className="mt-3 flex items-center gap-2 px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors disabled:opacity-50"
                >
                  <Play className="h-4 w-4" />
                  {processing ? 'Processing...' : 'Process Payroll'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <span className="text-sm text-green-600 font-medium">
              +{stats?.processed_employees || 0}/{stats?.total_employees || 0}
            </span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{stats?.total_employees || 0}</h3>
          <p className="text-gray-600 text-sm">Total Employees</p>
          <p className="text-xs text-gray-500 mt-1">{stats?.processed_employees || 0} processed this month</p>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <span className={`text-sm font-medium ${
              (stats?.monthly_comparison.percentage_change || 0) >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {(stats?.monthly_comparison.percentage_change || 0) >= 0 ? '+' : ''}{formatPercentage(stats?.monthly_comparison.percentage_change || 0)}
            </span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">
            {formatINRCompact(stats?.total_payroll_amount || 0)}
          </h3>
          <p className="text-gray-600 text-sm">Total Payroll</p>
          <p className="text-xs text-gray-500 mt-1">This month</p>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <span className="text-sm text-blue-600 font-medium">Avg</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">
            {formatINRCompact(stats?.average_salary || 0)}
          </h3>
          <p className="text-gray-600 text-sm">Average Salary</p>
          <p className="text-xs text-gray-500 mt-1">Per employee</p>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 bg-orange-100 rounded-lg">
              <AlertCircle className="h-6 w-6 text-orange-600" />
            </div>
            <span className="text-sm text-orange-600 font-medium">Pending</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{stats?.pending_approvals || 0}</h3>
          <p className="text-gray-600 text-sm">Pending Approvals</p>
          <p className="text-xs text-gray-500 mt-1">Require attention</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Payroll Periods</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">Manage payroll periods and processing schedules</p>
          <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Manage Periods
          </button>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileText className="h-5 w-5 text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Salary Management</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">Configure employee salary structures and revisions</p>
          <button className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            Manage Salaries
          </button>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <BarChart3 className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Reports & Analytics</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">Generate payroll reports and analytics</p>
          <button className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            View Reports
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Payroll Activity</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-green-100 rounded-full">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Payroll processed for December 2024</p>
                <p className="text-sm text-gray-600">22 employees processed successfully</p>
              </div>
              <span className="text-sm text-gray-500">2 hours ago</span>
            </div>
            
            <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-blue-100 rounded-full">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Salary revision approved</p>
                <p className="text-sm text-gray-600">John Doe - Software Engineer</p>
              </div>
              <span className="text-sm text-gray-500">1 day ago</span>
            </div>
            
            <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-orange-100 rounded-full">
                <AlertCircle className="h-4 w-4 text-orange-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Tax configuration updated</p>
                <p className="text-sm text-gray-600">Professional tax rates for 2024-25</p>
              </div>
              <span className="text-sm text-gray-500">3 days ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayrollDashboard;
