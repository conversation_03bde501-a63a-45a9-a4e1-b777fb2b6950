import React, { useState } from 'react';
import { User } from 'lucide-react';
import { teams } from '../data/teams';
import { TeamMember } from '../types/team';

// Utility function to find avatar path from teams data
const getAvatarFromTeams = (name?: string, email?: string): string | null => {
  if (!name) return null;

  // Normalize the name for comparison
  const normalizedName = name.toLowerCase().trim();

  // Extract first name for better matching
  const firstName = normalizedName.split(' ')[0];

  // Search through all teams for matching member
  for (const teamData of Object.values(teams)) {
    // Check SDM, TDM, CXM (filter out undefined values)
    const leaders = [teamData.sdm, teamData.tdm, teamData.cxm].filter(Boolean);
    for (const leader of leaders) {
      if (!leader || !leader.name) continue;
      const leaderName = leader.name.toLowerCase().trim();

      // Exact match
      if (leaderName === normalizedName) {
        return leader.avatar || null;
      }

      // First name match (for cases like "Selvendrane Developer" -> "Selvendrane")
      if (leaderName === firstName || leaderName.startsWith(firstName + ' ')) {
        return leader.avatar || null;
      }
    }

    // Check team members
    for (const member of teamData.members || []) {
      if (!member || !member.name) continue;
      const memberName = member.name.toLowerCase().trim();

      // Exact match
      if (memberName === normalizedName) {
        return member.avatar || null;
      }

      // First name match
      if (memberName === firstName || memberName.startsWith(firstName + ' ')) {
        return member.avatar || null;
      }
    }
  }

  // If no match found, try more flexible partial matching
  for (const teamData of Object.values(teams)) {
    const allMembers = [teamData.sdm, teamData.tdm, teamData.cxm].filter(Boolean).concat(teamData.members || []);
    for (const member of allMembers) {
      if (!member || !member.name) continue;
      const memberName = member.name.toLowerCase().trim();

      // Check if names contain each other (for partial matches)
      if (memberName.includes(firstName) || firstName.includes(memberName.split(' ')[0])) {
        return member.avatar || null;
      }
    }
  }

  return null;
};

interface ProfilePictureProps {
  userId?: string;
  name?: string;
  email?: string;
  profilePictureUrl?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showFallback?: boolean;
}

const ProfilePicture: React.FC<ProfilePictureProps> = ({
  userId,
  name,
  email,
  profilePictureUrl,
  size = 'md',
  className = '',
  showFallback = true
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Size configurations
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-16 h-16 text-lg',
    xl: 'w-24 h-24 text-xl'
  };

  const iconSizes = {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 32,
    xl: 48
  };

  // Generate initials from name
  const getInitials = (fullName: string): string => {
    return fullName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate a consistent color based on the name
  const getAvatarColor = (name: string): string => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-teal-500'
    ];
    
    const hash = name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  };

  // Construct profile picture URL
  const getProfilePictureUrl = (): string | null => {
    // First priority: use provided profilePictureUrl from database
    if (profilePictureUrl) return profilePictureUrl;

    // Second priority: get avatar from teams data (only if name exists)
    if (name) {
      const teamAvatar = getAvatarFromTeams(name, email);
      if (teamAvatar) return teamAvatar;
    }

    // Third priority: try conventional paths based on user data
    if (userId && name) {
      const possiblePaths = [
        `/profiles/${userId}.jpg`,
        `/profiles/${userId}.png`,
        `/profiles/${email?.split('@')[0]}.jpg`,
        `/profiles/${email?.split('@')[0]}.png`,
        `/profiles/${name.toLowerCase().replace(/\s+/g, '_')}.jpg`,
        `/profiles/${name.toLowerCase().replace(/\s+/g, '_')}.png`
      ];

      // Return the first possible path
      return possiblePaths[0];
    }

    return null;
  };

  const profileUrl = getProfilePictureUrl();
  const initials = name ? getInitials(name) : '??';
  const avatarColor = name ? getAvatarColor(name) : 'bg-gray-500';

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  // If we have a profile URL and no error, show the image
  if (profileUrl && !imageError) {
    return (
      <div className={`relative ${sizeClasses[size]} ${className}`}>
        <img
          src={profileUrl}
          alt={`${name}'s profile`}
          className={`w-full h-full rounded-full object-cover ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
        {imageLoading && (
          <div className={`absolute inset-0 rounded-full ${avatarColor} flex items-center justify-center`}>
            <div className="animate-pulse">
              <User size={iconSizes[size]} className="text-white" />
            </div>
          </div>
        )}
      </div>
    );
  }

  // Fallback to initials avatar
  if (showFallback) {
    return (
      <div className={`${sizeClasses[size]} ${avatarColor} rounded-full flex items-center justify-center text-white font-medium ${className}`}>
        {initials || <User size={iconSizes[size]} />}
      </div>
    );
  }

  // No fallback, show user icon
  return (
    <div className={`${sizeClasses[size]} bg-gray-300 rounded-full flex items-center justify-center ${className}`}>
      <User size={iconSizes[size]} className="text-gray-600" />
    </div>
  );
};

export default ProfilePicture;
