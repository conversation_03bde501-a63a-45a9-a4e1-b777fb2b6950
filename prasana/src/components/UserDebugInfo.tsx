import React from 'react';
import { useUser } from '../contexts/UserContext';

const UserDebugInfo: React.FC = () => {
  const { currentUser, isAdmin, isSuperAdmin } = useUser();

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">User Debug Info</h3>
      <div className="space-y-1">
        <div><strong>Email:</strong> {currentUser?.email || 'Not logged in'}</div>
        <div><strong>Name:</strong> {currentUser?.name || 'N/A'}</div>
        <div><strong>ID:</strong> {currentUser?.id || 'N/A'}</div>
        <div><strong>Is Admin:</strong> {isAdmin ? '✅ Yes' : '❌ No'}</div>
        <div><strong>Is Super Admin:</strong> {isSuperAdmin ? '✅ Yes' : '❌ No'}</div>
        <div><strong>Role:</strong> {currentUser?.role || 'N/A'}</div>
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-600">
        <div className="text-xs text-gray-300">
          <div>Admin Emails: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL></div>
          <div className="mt-1">Super Admin Emails: <EMAIL>, <EMAIL>, <EMAIL></div>
        </div>
      </div>
    </div>
  );
};

export default UserDebugInfo;
