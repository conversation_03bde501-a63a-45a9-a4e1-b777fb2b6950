import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Shield,
  Users,
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  User<PERSON>heck,
  UserX,
  Settings,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  BarChart3,
  FileText,
  Layers,
  Zap,
  Target,
  TrendingUp,
  Archive,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Copy,
  Star
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { roleManagementService } from '../../services/roleManagementService';
import {
  Role,
  EmployeeRole,
  PermissionGroup,
  RoleTemplate,
  RoleApprovalRequest,
  RoleAuditLog,
  RoleFilters,
  EmployeeRoleFilters,
  RoleFormData,
  BulkAssignmentData,
  RoleAssignmentFormData,
  RoleHierarchy,
  RoleStatistics,
  ROLE_CATEGORIES
} from '../../types/roleManagement';

// Enhanced component state interfaces
interface ComponentState {
  roles: Role[];
  employeeRoles: EmployeeRole[];
  permissionGroups: PermissionGroup[];
  roleTemplates: RoleTemplate[];
  approvalRequests: RoleApprovalRequest[];
  auditLogs: RoleAuditLog[];
  employees: any[];
  roleHierarchy: RoleHierarchy[];
  statistics: RoleStatistics | null;
  loading: boolean;
  error: string | null;
}

interface UIState {
  activeTab: 'my-roles' | 'roles' | 'assignments' | 'templates' | 'approvals' | 'audit' | 'analytics';
  showCreateRole: boolean;
  showAssignRole: boolean;
  showBulkAssign: boolean;
  showRoleHierarchy: boolean;
  selectedRole: Role | null;
  selectedEmployees: string[];
  expandedRoles: Set<string>;
}

const RoleManagement: React.FC = () => {
  const { currentEmployee } = useHRMS();

  // Component state
  const [state, setState] = useState<ComponentState>({
    roles: [],
    employeeRoles: [],
    permissionGroups: [],
    roleTemplates: [],
    approvalRequests: [],
    auditLogs: [],
    employees: [],
    roleHierarchy: [],
    statistics: null,
    loading: true,
    error: null
  });

  // UI state
  const [uiState, setUIState] = useState<UIState>({
    activeTab: 'my-roles',
    showCreateRole: false,
    showAssignRole: false,
    showBulkAssign: false,
    showRoleHierarchy: false,
    selectedRole: null,
    selectedEmployees: [],
    expandedRoles: new Set()
  });

  // Filters
  const [roleFilters, setRoleFilters] = useState<RoleFilters>({
    search: '',
    category: '',
    level: null,
    status: 'active',
    permission_group: '',
    has_expiry: null,
    approval_status: ''
  });

  const [employeeRoleFilters, setEmployeeRoleFilters] = useState<EmployeeRoleFilters>({
    search: '',
    role_id: '',
    department: '',
    approval_status: '',
    expiry_status: 'all'
  });

  // Form data
  const [newRole, setNewRole] = useState<RoleFormData>({
    name: '',
    description: '',
    permissions: [],
    permission_groups: [],
    role_level: 1
  });

  const [assignRoleForm, setAssignRoleForm] = useState<RoleAssignmentFormData>({
    employee_id: '',
    role_id: '',
    require_approval: false,
    priority: 'normal'
  });

  const [bulkAssignForm, setBulkAssignForm] = useState<BulkAssignmentData>({
    employee_ids: [],
    role_id: '',
    require_approval: false
  });

  // Memoized computed values
  const myRoles = useMemo(() =>
    state.employeeRoles.filter(er => er.employee_id === currentEmployee?.id),
    [state.employeeRoles, currentEmployee?.id]
  );

  const filteredRoles = useMemo(() => {
    return state.roles.filter(role => {
      if (roleFilters.search && !role.name.toLowerCase().includes(roleFilters.search.toLowerCase()) &&
          !role.description.toLowerCase().includes(roleFilters.search.toLowerCase())) {
        return false;
      }
      if (roleFilters.status === 'active' && !role.is_active) return false;
      if (roleFilters.status === 'inactive' && role.is_active) return false;
      if (roleFilters.level && role.role_level !== roleFilters.level) return false;
      if (roleFilters.category && role.template_category !== roleFilters.category) return false;
      return true;
    });
  }, [state.roles, roleFilters]);

  const filteredEmployeeRoles = useMemo(() => {
    return state.employeeRoles.filter(er => {
      if (employeeRoleFilters.search) {
        const searchLower = employeeRoleFilters.search.toLowerCase();
        if (!er.employee.first_name.toLowerCase().includes(searchLower) &&
            !er.employee.last_name.toLowerCase().includes(searchLower) &&
            !er.employee.email.toLowerCase().includes(searchLower)) {
          return false;
        }
      }
      if (employeeRoleFilters.role_id && er.role_id !== employeeRoleFilters.role_id) return false;
      if (employeeRoleFilters.approval_status && er.approval_status !== employeeRoleFilters.approval_status) return false;
      if (employeeRoleFilters.expiry_status === 'expiring_soon') {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        if (!er.expiry_date || new Date(er.expiry_date) > thirtyDaysFromNow) return false;
      }
      if (employeeRoleFilters.expiry_status === 'expired') {
        if (!er.expiry_date || new Date(er.expiry_date) > new Date()) return false;
      }
      return true;
    });
  }, [state.employeeRoles, employeeRoleFilters]);

  // Data loading functions
  useEffect(() => {
    if (currentEmployee) {
      loadInitialData();
    }
  }, [currentEmployee]);

  const loadInitialData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const [
        roles,
        employeeRoles,
        permissionGroups,
        roleTemplates,
        employees,
        statistics
      ] = await Promise.all([
        roleManagementService.getRoles(roleFilters),
        roleManagementService.getEmployeeRoles(employeeRoleFilters),
        roleManagementService.getPermissionGroups(),
        roleManagementService.getRoleTemplates(),
        loadEmployees(),
        roleManagementService.getRoleStatistics()
      ]);

      setState(prev => ({
        ...prev,
        roles,
        employeeRoles,
        permissionGroups,
        roleTemplates,
        employees,
        statistics,
        loading: false
      }));
    } catch (error) {
      console.error('Error loading role data:', error);
      setState(prev => ({
        ...prev,
        error: (error as Error).message,
        loading: false
      }));
    }
  }, [roleFilters, employeeRoleFilters]);

  const loadEmployees = async () => {
    // This would typically come from an employee service
    // For now, we'll use a simplified version
    const { data, error } = await supabase
      .from('employees')
      .select('id, first_name, last_name, email, designation, department')
      .eq('status', 'active')
      .order('first_name');

    if (error) throw error;
    return data || [];
  };

  const loadRoles = async () => {
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (!error && data) {
      setRoles(data);
    }
  };

  const loadEmployeeRoles = async () => {
    const { data, error } = await supabase
      .from('employee_roles')
      .select(`
        *,
        role:roles(*),
        employee:employees(first_name, last_name, email, designation)
      `)
      .eq('is_active', true)
      .order('assigned_date', { ascending: false });

    if (!error && data) {
      setEmployeeRoles(data as any);
    }
  };

  const loadEmployees = async () => {
    const { data, error } = await supabase
      .from('employees')
      .select('id, first_name, last_name, email, designation')
      .eq('status', 'active')
      .order('first_name');

    if (!error && data) {
      setEmployees(data);
    }
  };

  const handleCreateRole = async () => {
    if (!newRole.name.trim()) return;

    try {
      const { error } = await supabase
        .from('roles')
        .insert([{
          name: newRole.name,
          description: newRole.description,
          permissions: newRole.permissions
        }]);

      if (!error) {
        setNewRole({ name: '', description: '', permissions: [] });
        setShowCreateRole(false);
        loadRoles();
      }
    } catch (error) {
      console.error('Error creating role:', error);
    }
  };

  const handleAssignRole = async () => {
    if (!assignRole.employee_id || !assignRole.role_id) return;

    try {
      const { error } = await supabase
        .from('employee_roles')
        .insert([{
          employee_id: assignRole.employee_id,
          role_id: assignRole.role_id,
          assigned_by: currentEmployee?.id
        }]);

      if (!error) {
        setAssignRole({ employee_id: '', role_id: '' });
        setShowAssignRole(false);
        loadEmployeeRoles();
      }
    } catch (error) {
      console.error('Error assigning role:', error);
    }
  };

  const removeRole = async (employeeRoleId: string) => {
    if (!confirm('Are you sure you want to remove this role assignment?')) return;

    try {
      const { error } = await supabase
        .from('employee_roles')
        .update({ is_active: false })
        .eq('id', employeeRoleId);

      if (!error) {
        loadEmployeeRoles();
      }
    } catch (error) {
      console.error('Error removing role:', error);
    }
  };

  const togglePermission = (permission: string) => {
    setNewRole(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const getMyRoles = () => {
    return employeeRoles.filter(er => er.employee_id === currentEmployee?.id);
  };

  const filteredEmployeeRoles = employeeRoles.filter(er => 
    er.employee.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    er.employee.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    er.role.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Role Management</h1>
        <p className="text-gray-600">Manage roles, permissions, and role assignments</p>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'my-roles', name: 'My Roles', icon: UserCheck },
              { id: 'roles', name: 'All Roles', icon: Shield },
              { id: 'assignments', name: 'Role Assignments', icon: Users }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'my-roles' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">My Current Roles</h3>
              {getMyRoles().length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getMyRoles().map((employeeRole) => (
                    <div key={employeeRole.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{employeeRole.role.name}</h4>
                        <span className="bg-green-100 text-green-800 px-2 py-1 text-xs rounded-full">
                          Active
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{employeeRole.role.description}</p>
                      <div className="space-y-2">
                        <p className="text-xs font-medium text-gray-700">Permissions:</p>
                        <div className="flex flex-wrap gap-1">
                          {employeeRole.role.permissions.map((permission) => (
                            <span
                              key={permission}
                              className="bg-blue-100 text-blue-800 px-2 py-1 text-xs rounded"
                            >
                              {permission.replace('_', ' ')}
                            </span>
                          ))}
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-3">
                        Assigned: {new Date(employeeRole.assigned_date).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <UserX className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No roles assigned yet</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">System Roles</h3>
                <button
                  onClick={() => setShowCreateRole(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Create Role</span>
                </button>
              </div>

              {/* Create Role Form */}
              {showCreateRole && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                      <input
                        type="text"
                        value={newRole.name}
                        onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., HR Manager"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <input
                        type="text"
                        value={newRole.description}
                        onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Role description"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {availablePermissions.map((permission) => (
                        <label key={permission} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={newRole.permissions.includes(permission)}
                            onChange={() => togglePermission(permission)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700">{permission.replace('_', ' ')}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleCreateRole}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                    >
                      Create Role
                    </button>
                    <button
                      onClick={() => setShowCreateRole(false)}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Roles List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {roles.map((role) => (
                  <div key={role.id} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">{role.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{role.description}</p>
                    <div className="space-y-2">
                      <p className="text-xs font-medium text-gray-700">Permissions ({role.permissions.length}):</p>
                      <div className="flex flex-wrap gap-1">
                        {role.permissions.slice(0, 3).map((permission) => (
                          <span
                            key={permission}
                            className="bg-gray-100 text-gray-800 px-2 py-1 text-xs rounded"
                          >
                            {permission.replace('_', ' ')}
                          </span>
                        ))}
                        {role.permissions.length > 3 && (
                          <span className="bg-gray-100 text-gray-800 px-2 py-1 text-xs rounded">
                            +{role.permissions.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'assignments' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Role Assignments</h3>
                <button
                  onClick={() => setShowAssignRole(true)}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <UserCheck className="w-4 h-4" />
                  <span>Assign Role</span>
                </button>
              </div>

              {/* Search */}
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Search employees or roles..."
                />
              </div>

              {/* Assign Role Form */}
              {showAssignRole && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Employee</label>
                      <select
                        value={assignRole.employee_id}
                        onChange={(e) => setAssignRole({ ...assignRole, employee_id: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select Employee</option>
                        {employees.map((emp) => (
                          <option key={emp.id} value={emp.id}>
                            {emp.first_name} {emp.last_name} - {emp.designation}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                      <select
                        value={assignRole.role_id}
                        onChange={(e) => setAssignRole({ ...assignRole, role_id: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select Role</option>
                        {roles.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleAssignRole}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                    >
                      Assign Role
                    </button>
                    <button
                      onClick={() => setShowAssignRole(false)}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Assignments List */}
              <div className="space-y-4">
                {filteredEmployeeRoles.map((employeeRole) => (
                  <div key={employeeRole.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="bg-blue-100 p-2 rounded-full">
                          <Users className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">
                            {employeeRole.employee.first_name} {employeeRole.employee.last_name}
                          </h4>
                          <p className="text-sm text-gray-600">{employeeRole.employee.designation}</p>
                          <p className="text-xs text-gray-500">{employeeRole.employee.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{employeeRole.role.name}</p>
                          <p className="text-xs text-gray-500">
                            Assigned: {new Date(employeeRole.assigned_date).toLocaleDateString()}
                          </p>
                        </div>
                        <button
                          onClick={() => removeRole(employeeRole.id)}
                          className="text-red-600 hover:text-red-800"
                          title="Remove Role"
                        >
                          <UserX className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredEmployeeRoles.length === 0 && (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No role assignments found</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RoleManagement;
