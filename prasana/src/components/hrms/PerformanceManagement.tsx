import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  Target, 
  Award, 
  Calendar, 
  Plus,
  Eye,
  Edit,
  Star,
  BarChart3,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { supabase } from '../../supabaseClient';

interface Goal {
  id: string;
  title: string;
  description: string;
  target_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  progress: number;
  category: 'professional' | 'personal' | 'skill' | 'project';
  created_at: string;
}

interface Review {
  id: string;
  period: string;
  review_type: 'quarterly' | 'half_yearly' | 'annual' | 'probation';
  self_rating?: number;
  manager_rating?: number;
  final_rating?: number;
  goals_achieved: number;
  total_goals: number;
  strengths?: string;
  areas_for_improvement?: string;
  feedback: string;
  manager_feedback?: string;
  development_plan?: string;
  status: 'draft' | 'self_submitted' | 'manager_review' | 'completed' | 'cancelled';
  due_date?: string;
  completed_date?: string;
  created_at: string;
}

interface Skill {
  id: string;
  skill_name: string;
  proficiency_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  category: 'technical' | 'soft' | 'leadership' | 'domain';
  created_at: string;
}

const PerformanceManagement: React.FC = () => {
  const { currentEmployee } = useHRMS();
  const [goals, setGoals] = useState<Goal[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'goals' | 'reviews' | 'skills' | 'analytics'>('goals');
  const [showAddGoal, setShowAddGoal] = useState(false);
  const [showAddSkill, setShowAddSkill] = useState(false);
  const [newGoal, setNewGoal] = useState({
    title: '',
    description: '',
    target_date: '',
    category: 'professional' as const
  });
  const [newSkill, setNewSkill] = useState({
    skill_name: '',
    proficiency_level: 'beginner' as const,
    category: 'technical' as const
  });

  useEffect(() => {
    if (currentEmployee) {
      loadPerformanceData();
    }
  }, [currentEmployee]);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      await Promise.all([loadGoals(), loadReviews(), loadSkills()]);
    } catch (error) {
      console.error('Error loading performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadGoals = async () => {
    if (!currentEmployee) return;

    const { data, error } = await supabase
      .from('performance_goals')
      .select('*')
      .eq('employee_id', currentEmployee.id)
      .order('created_at', { ascending: false });

    if (!error && data) {
      setGoals(data);
    }
  };

  const loadReviews = async () => {
    if (!currentEmployee) return;

    const { data, error } = await supabase
      .from('performance_reviews')
      .select('*')
      .eq('employee_id', currentEmployee.id)
      .order('created_at', { ascending: false });

    if (!error && data) {
      setReviews(data);
    }
  };

  const loadSkills = async () => {
    if (!currentEmployee) return;

    const { data, error } = await supabase
      .from('employee_skills')
      .select('*')
      .eq('employee_id', currentEmployee.id)
      .order('category', { ascending: true });

    if (!error && data) {
      setSkills(data);
    }
  };

  const handleAddGoal = async () => {
    if (!currentEmployee || !newGoal.title.trim()) return;

    try {
      const { error } = await supabase
        .from('performance_goals')
        .insert([{
          employee_id: currentEmployee.id,
          title: newGoal.title,
          description: newGoal.description,
          target_date: newGoal.target_date,
          category: newGoal.category,
          status: 'not_started',
          progress: 0
        }]);

      if (!error) {
        setNewGoal({ title: '', description: '', target_date: '', category: 'professional' });
        setShowAddGoal(false);
        loadGoals();
      }
    } catch (error) {
      console.error('Error adding goal:', error);
    }
  };

  const updateGoalProgress = async (goalId: string, progress: number) => {
    const status = progress === 100 ? 'completed' : progress > 0 ? 'in_progress' : 'not_started';

    const { error } = await supabase
      .from('performance_goals')
      .update({ progress, status })
      .eq('id', goalId);

    if (!error) {
      loadGoals();
    }
  };

  const handleAddSkill = async () => {
    if (!currentEmployee || !newSkill.skill_name.trim()) return;

    try {
      const { error } = await supabase
        .from('employee_skills')
        .insert([{
          employee_id: currentEmployee.id,
          skill_name: newSkill.skill_name,
          proficiency_level: newSkill.proficiency_level,
          category: newSkill.category
        }]);

      if (!error) {
        setNewSkill({ skill_name: '', proficiency_level: 'beginner', category: 'technical' });
        setShowAddSkill(false);
        loadSkills();
      }
    } catch (error) {
      console.error('Error adding skill:', error);
    }
  };

  const updateSkillProficiency = async (skillId: string, proficiency: string) => {
    const { error } = await supabase
      .from('employee_skills')
      .update({ proficiency_level: proficiency })
      .eq('id', skillId);

    if (!error) {
      loadSkills();
    }
  };

  const deleteSkill = async (skillId: string) => {
    if (!confirm('Are you sure you want to remove this skill?')) return;

    const { error } = await supabase
      .from('employee_skills')
      .delete()
      .eq('id', skillId);

    if (!error) {
      loadSkills();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'professional': return <TrendingUp className="w-4 h-4" />;
      case 'skill': return <Award className="w-4 h-4" />;
      case 'project': return <Target className="w-4 h-4" />;
      default: return <Star className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Performance Management</h1>
        <p className="text-gray-600">Track your goals, reviews, and professional development</p>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'goals', name: 'Goals', icon: Target },
              { id: 'skills', name: 'Skills', icon: Award },
              { id: 'reviews', name: 'Reviews', icon: Star },
              { id: 'analytics', name: 'Analytics', icon: BarChart3 }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'goals' && (
            <div className="space-y-6">
              {/* Add Goal Button */}
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">My Goals</h3>
                <button
                  onClick={() => setShowAddGoal(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Goal</span>
                </button>
              </div>

              {/* Add Goal Form */}
              {showAddGoal && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Goal Title</label>
                      <input
                        type="text"
                        value={newGoal.title}
                        onChange={(e) => setNewGoal({ ...newGoal, title: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter goal title"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                      <select
                        value={newGoal.category}
                        onChange={(e) => setNewGoal({ ...newGoal, category: e.target.value as any })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="professional">Professional</option>
                        <option value="skill">Skill Development</option>
                        <option value="project">Project</option>
                        <option value="personal">Personal</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      value={newGoal.description}
                      onChange={(e) => setNewGoal({ ...newGoal, description: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Describe your goal"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Target Date</label>
                    <input
                      type="date"
                      value={newGoal.target_date}
                      onChange={(e) => setNewGoal({ ...newGoal, target_date: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleAddGoal}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                    >
                      Add Goal
                    </button>
                    <button
                      onClick={() => setShowAddGoal(false)}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Goals List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {goals.map((goal) => (
                  <div key={goal.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        {getCategoryIcon(goal.category)}
                        <h4 className="font-medium text-gray-900">{goal.title}</h4>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(goal.status)}`}>
                        {goal.status.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600">{goal.description}</p>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{goal.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${goal.progress}%` }}
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>Target: {new Date(goal.target_date).toLocaleDateString()}</span>
                      <div className="flex space-x-1">
                        {[0, 25, 50, 75, 100].map((progress) => (
                          <button
                            key={progress}
                            onClick={() => updateGoalProgress(goal.id, progress)}
                            className={`px-2 py-1 text-xs rounded ${
                              goal.progress === progress
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                            }`}
                          >
                            {progress}%
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {goals.length === 0 && (
                <div className="text-center py-8">
                  <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No goals set yet. Add your first goal to get started!</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'skills' && (
            <div className="space-y-6">
              {/* Add Skill Button */}
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">My Skills</h3>
                <button
                  onClick={() => setShowAddSkill(true)}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Add Skill</span>
                </button>
              </div>

              {/* Add Skill Form */}
              {showAddSkill && (
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Skill Name</label>
                      <input
                        type="text"
                        value={newSkill.skill_name}
                        onChange={(e) => setNewSkill({ ...newSkill, skill_name: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g., React.js, Leadership"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Proficiency Level</label>
                      <select
                        value={newSkill.proficiency_level}
                        onChange={(e) => setNewSkill({ ...newSkill, proficiency_level: e.target.value as any })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                        <option value="expert">Expert</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                      <select
                        value={newSkill.category}
                        onChange={(e) => setNewSkill({ ...newSkill, category: e.target.value as any })}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="technical">Technical</option>
                        <option value="soft">Soft Skills</option>
                        <option value="leadership">Leadership</option>
                        <option value="domain">Domain Knowledge</option>
                      </select>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleAddSkill}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
                    >
                      Add Skill
                    </button>
                    <button
                      onClick={() => setShowAddSkill(false)}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Skills by Category */}
              {['technical', 'soft', 'leadership', 'domain'].map((category) => {
                const categorySkills = skills.filter(skill => skill.category === category);
                if (categorySkills.length === 0) return null;

                return (
                  <div key={category} className="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3 capitalize">
                      {category === 'soft' ? 'Soft Skills' :
                       category === 'domain' ? 'Domain Knowledge' :
                       category.charAt(0).toUpperCase() + category.slice(1)} Skills
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {categorySkills.map((skill) => (
                        <div key={skill.id} className="border border-gray-200 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-sm">{skill.skill_name}</h5>
                            <button
                              onClick={() => deleteSkill(skill.id)}
                              className="text-red-500 hover:text-red-700"
                              title="Remove skill"
                            >
                              ×
                            </button>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">Level:</span>
                            <select
                              value={skill.proficiency_level}
                              onChange={(e) => updateSkillProficiency(skill.id, e.target.value)}
                              className="text-xs border border-gray-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500"
                            >
                              <option value="beginner">Beginner</option>
                              <option value="intermediate">Intermediate</option>
                              <option value="advanced">Advanced</option>
                              <option value="expert">Expert</option>
                            </select>
                          </div>
                          <div className="mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${
                                  skill.proficiency_level === 'expert' ? 'bg-purple-500' :
                                  skill.proficiency_level === 'advanced' ? 'bg-green-500' :
                                  skill.proficiency_level === 'intermediate' ? 'bg-blue-500' :
                                  'bg-yellow-500'
                                }`}
                                style={{
                                  width: skill.proficiency_level === 'expert' ? '100%' :
                                         skill.proficiency_level === 'advanced' ? '75%' :
                                         skill.proficiency_level === 'intermediate' ? '50%' : '25%'
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}

              {skills.length === 0 && (
                <div className="text-center py-8">
                  <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No skills added yet. Add your first skill to get started!</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Performance Reviews</h3>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800">Performance reviews will be available during review periods. Check with your manager for upcoming review cycles.</p>
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Performance Analytics</h3>
              
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-800">Completed Goals</span>
                  </div>
                  <p className="text-2xl font-bold text-green-900 mt-2">
                    {goals.filter(g => g.status === 'completed').length}
                  </p>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">In Progress</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-900 mt-2">
                    {goals.filter(g => g.status === 'in_progress').length}
                  </p>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Target className="w-5 h-5 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-800">Total Goals</span>
                  </div>
                  <p className="text-2xl font-bold text-yellow-900 mt-2">{goals.length}</p>
                </div>
                
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-800">Avg Progress</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-900 mt-2">
                    {goals.length > 0 ? Math.round(goals.reduce((sum, g) => sum + g.progress, 0) / goals.length) : 0}%
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PerformanceManagement;
