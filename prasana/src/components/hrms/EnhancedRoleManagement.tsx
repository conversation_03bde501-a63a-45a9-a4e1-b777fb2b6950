import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Shield, 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Check, 
  X,
  UserCheck,
  UserX,
  Settings,
  Eye,
  Search,
  Filter,
  Download,
  Upload,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  BarChart3,
  FileText,
  Layers,
  Zap,
  Target,
  TrendingUp,
  Archive,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Copy,
  Star
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { supabase } from '../../supabaseClient';
import { roleManagementService } from '../../services/roleManagementService';
import {
  Role,
  EmployeeRole,
  PermissionGroup,
  RoleTemplate,
  RoleApprovalRequest,
  RoleAuditLog,
  RoleFilters,
  EmployeeRoleFilters,
  RoleFormData,
  BulkAssignmentData,
  RoleAssignmentFormData,
  RoleHierarchy,
  RoleStatistics,
  ROLE_CATEGORIES
} from '../../types/roleManagement';

// Component interfaces
interface ComponentState {
  roles: Role[];
  employeeRoles: EmployeeRole[];
  permissionGroups: PermissionGroup[];
  roleTemplates: RoleTemplate[];
  approvalRequests: RoleApprovalRequest[];
  auditLogs: RoleAuditLog[];
  employees: any[];
  roleHierarchy: RoleHierarchy[];
  statistics: RoleStatistics | null;
  loading: boolean;
  error: string | null;
}

interface UIState {
  activeTab: 'my-roles' | 'roles' | 'assignments' | 'templates' | 'approvals' | 'audit' | 'analytics';
  showCreateRole: boolean;
  showAssignRole: boolean;
  showBulkAssign: boolean;
  showRoleHierarchy: boolean;
  selectedRole: Role | null;
  selectedEmployees: string[];
  expandedRoles: Set<string>;
}

const EnhancedRoleManagement: React.FC = () => {
  const { currentEmployee } = useHRMS();
  
  // Component state
  const [state, setState] = useState<ComponentState>({
    roles: [],
    employeeRoles: [],
    permissionGroups: [],
    roleTemplates: [],
    approvalRequests: [],
    auditLogs: [],
    employees: [],
    roleHierarchy: [],
    statistics: null,
    loading: true,
    error: null
  });

  // UI state
  const [uiState, setUIState] = useState<UIState>({
    activeTab: 'my-roles',
    showCreateRole: false,
    showAssignRole: false,
    showBulkAssign: false,
    showRoleHierarchy: false,
    selectedRole: null,
    selectedEmployees: [],
    expandedRoles: new Set()
  });

  // Filters
  const [roleFilters, setRoleFilters] = useState<RoleFilters>({
    search: '',
    category: '',
    level: null,
    status: 'active',
    permission_group: '',
    has_expiry: null,
    approval_status: ''
  });

  const [employeeRoleFilters, setEmployeeRoleFilters] = useState<EmployeeRoleFilters>({
    search: '',
    role_id: '',
    department: '',
    approval_status: '',
    expiry_status: 'all'
  });

  // Form data
  const [newRole, setNewRole] = useState<RoleFormData>({
    name: '',
    description: '',
    permissions: [],
    permission_groups: [],
    role_level: 1
  });

  const [assignRoleForm, setAssignRoleForm] = useState<RoleAssignmentFormData>({
    employee_id: '',
    role_id: '',
    require_approval: false,
    priority: 'normal'
  });

  const [bulkAssignForm, setBulkAssignForm] = useState<BulkAssignmentData>({
    employee_ids: [],
    role_id: '',
    require_approval: false
  });

  // Memoized computed values
  const myRoles = useMemo(() => 
    state.employeeRoles.filter(er => er.employee_id === currentEmployee?.id),
    [state.employeeRoles, currentEmployee?.id]
  );

  const filteredRoles = useMemo(() => {
    return state.roles.filter(role => {
      if (roleFilters.search && !role.name.toLowerCase().includes(roleFilters.search.toLowerCase()) &&
          !role.description.toLowerCase().includes(roleFilters.search.toLowerCase())) {
        return false;
      }
      if (roleFilters.status === 'active' && !role.is_active) return false;
      if (roleFilters.status === 'inactive' && role.is_active) return false;
      if (roleFilters.level && role.role_level !== roleFilters.level) return false;
      if (roleFilters.category && role.template_category !== roleFilters.category) return false;
      return true;
    });
  }, [state.roles, roleFilters]);

  const filteredEmployeeRoles = useMemo(() => {
    return state.employeeRoles.filter(er => {
      if (employeeRoleFilters.search) {
        const searchLower = employeeRoleFilters.search.toLowerCase();
        if (!er.employee.first_name.toLowerCase().includes(searchLower) &&
            !er.employee.last_name.toLowerCase().includes(searchLower) &&
            !er.employee.email.toLowerCase().includes(searchLower)) {
          return false;
        }
      }
      if (employeeRoleFilters.role_id && er.role_id !== employeeRoleFilters.role_id) return false;
      if (employeeRoleFilters.approval_status && er.approval_status !== employeeRoleFilters.approval_status) return false;
      if (employeeRoleFilters.expiry_status === 'expiring_soon') {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        if (!er.expiry_date || new Date(er.expiry_date) > thirtyDaysFromNow) return false;
      }
      if (employeeRoleFilters.expiry_status === 'expired') {
        if (!er.expiry_date || new Date(er.expiry_date) > new Date()) return false;
      }
      return true;
    });
  }, [state.employeeRoles, employeeRoleFilters]);

  // Data loading functions
  useEffect(() => {
    if (currentEmployee) {
      loadInitialData();
    }
  }, [currentEmployee]);

  const loadInitialData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const [
        roles,
        employeeRoles,
        permissionGroups,
        roleTemplates,
        employees,
        statistics
      ] = await Promise.all([
        roleManagementService.getRoles(roleFilters),
        roleManagementService.getEmployeeRoles(employeeRoleFilters),
        roleManagementService.getPermissionGroups(),
        roleManagementService.getRoleTemplates(),
        loadEmployees(),
        roleManagementService.getRoleStatistics()
      ]);

      setState(prev => ({
        ...prev,
        roles,
        employeeRoles,
        permissionGroups,
        roleTemplates,
        employees,
        statistics,
        loading: false
      }));
    } catch (error) {
      console.error('Error loading role data:', error);
      setState(prev => ({
        ...prev,
        error: (error as Error).message,
        loading: false
      }));
    }
  }, [roleFilters, employeeRoleFilters]);

  const loadEmployees = async () => {
    const { data, error } = await supabase
      .from('employees')
      .select('id, first_name, last_name, email, designation, department')
      .eq('status', 'active')
      .order('first_name');

    if (error) throw error;
    return data || [];
  };

  // Action handlers
  const handleCreateRole = async () => {
    try {
      const role = await roleManagementService.createRole(newRole, currentEmployee?.id);
      setState(prev => ({ ...prev, roles: [...prev.roles, role] }));
      setNewRole({
        name: '',
        description: '',
        permissions: [],
        permission_groups: [],
        role_level: 1
      });
      setUIState(prev => ({ ...prev, showCreateRole: false }));
    } catch (error) {
      console.error('Error creating role:', error);
    }
  };

  const handleAssignRole = async () => {
    try {
      const assignment = await roleManagementService.assignRole(assignRoleForm, currentEmployee?.id || '');
      setState(prev => ({ ...prev, employeeRoles: [...prev.employeeRoles, assignment] }));
      setAssignRoleForm({
        employee_id: '',
        role_id: '',
        require_approval: false,
        priority: 'normal'
      });
      setUIState(prev => ({ ...prev, showAssignRole: false }));
    } catch (error) {
      console.error('Error assigning role:', error);
      alert((error as Error).message);
    }
  };

  const handleBulkAssign = async () => {
    try {
      await roleManagementService.bulkAssignRoles(bulkAssignForm, currentEmployee?.id || '');
      setBulkAssignForm({
        employee_ids: [],
        role_id: '',
        require_approval: false
      });
      setUIState(prev => ({ ...prev, showBulkAssign: false }));
      loadInitialData(); // Refresh data
    } catch (error) {
      console.error('Error bulk assigning roles:', error);
      alert((error as Error).message);
    }
  };

  const handleRemoveRole = async (employeeRoleId: string) => {
    if (!confirm('Are you sure you want to remove this role assignment?')) return;

    try {
      await roleManagementService.removeRole(employeeRoleId, currentEmployee?.id || '');
      setState(prev => ({
        ...prev,
        employeeRoles: prev.employeeRoles.filter(er => er.id !== employeeRoleId)
      }));
    } catch (error) {
      console.error('Error removing role:', error);
    }
  };

  const togglePermissionGroup = (groupId: string) => {
    setNewRole(prev => ({
      ...prev,
      permission_groups: prev.permission_groups.includes(groupId)
        ? prev.permission_groups.filter(id => id !== groupId)
        : [...prev.permission_groups, groupId]
    }));
  };

  const togglePermission = (permission: string) => {
    setNewRole(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  if (state.loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-red-600" />
          <span className="text-red-800 font-medium">Error loading role data</span>
        </div>
        <p className="text-red-600 mt-2">{state.error}</p>
        <button
          onClick={loadInitialData}
          className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Statistics */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Enhanced Role Management</h1>
            <p className="text-gray-600">Advanced role, permission, and access control management</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setUIState(prev => ({ ...prev, showRoleHierarchy: !prev.showRoleHierarchy }))}
              className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <Layers className="w-4 h-4" />
              <span>Hierarchy</span>
            </button>
            <button
              onClick={loadInitialData}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Quick Statistics */}
        {state.statistics && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Total Roles</span>
              </div>
              <p className="text-xl font-bold text-blue-900">{state.statistics.total_roles}</p>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Active Assignments</span>
              </div>
              <p className="text-xl font-bold text-green-900">{state.statistics.total_assignments}</p>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">Pending Approvals</span>
              </div>
              <p className="text-xl font-bold text-yellow-900">{state.statistics.pending_approvals}</p>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Expiring Soon</span>
              </div>
              <p className="text-xl font-bold text-red-900">{state.statistics.expiring_soon}</p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Navigation Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'my-roles', name: 'My Roles', icon: UserCheck, count: myRoles.length },
              { id: 'roles', name: 'All Roles', icon: Shield, count: state.roles.length },
              { id: 'assignments', name: 'Assignments', icon: Users, count: state.employeeRoles.length },
              { id: 'templates', name: 'Templates', icon: Copy, count: state.roleTemplates.length },
              { id: 'approvals', name: 'Approvals', icon: CheckCircle, count: state.approvalRequests.length },
              { id: 'audit', name: 'Audit Log', icon: FileText, count: state.auditLogs.length },
              { id: 'analytics', name: 'Analytics', icon: BarChart3 }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setUIState(prev => ({ ...prev, activeTab: tab.id as any }))}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  uiState.activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
                {tab.count !== undefined && (
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    uiState.activeTab === tab.id
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content will be added in the next part */}
        <div className="p-6">
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">
              <Settings className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-gray-500">Enhanced Role Management content will be implemented here</p>
            <p className="text-sm text-gray-400 mt-2">
              Current tab: {uiState.activeTab} | My roles: {myRoles.length} | Total roles: {state.roles.length}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedRoleManagement;
