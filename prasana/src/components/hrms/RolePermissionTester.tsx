import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Check, 
  X, 
  AlertTriangle, 
  User, 
  Users, 
  Calendar, 
  Clock, 
  FileText, 
  TrendingUp,
  DollarSign,
  Settings,
  BarChart3
} from 'lucide-react';
import { useHRMS } from '../../contexts/HRMSContext';
import { supabase } from '../../supabaseClient';

interface UserPermissions {
  employee_access: boolean;
  leave_access: boolean;
  timesheet_access: boolean;
  document_access: boolean;
  performance_access: boolean;
  team_management: boolean;
  leave_approval: boolean;
  performance_review: boolean;
  document_approval: boolean;
  hr_management: boolean;
  payroll_management: boolean;
  policy_management: boolean;
  role_management: boolean;
  system_reports: boolean;
  admin_access: boolean;
}

interface PermissionTest {
  permission: keyof UserPermissions;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  testFunction: () => Promise<boolean>;
}

const RolePermissionTester: React.FC = () => {
  const { currentEmployee } = useHRMS();
  const [userPermissions, setUserPermissions] = useState<UserPermissions>({
    employee_access: false,
    leave_access: false,
    timesheet_access: false,
    document_access: false,
    performance_access: false,
    team_management: false,
    leave_approval: false,
    performance_review: false,
    document_approval: false,
    hr_management: false,
    payroll_management: false,
    policy_management: false,
    role_management: false,
    system_reports: false,
    admin_access: false
  });
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [testing, setTesting] = useState(false);
  const [userRoles, setUserRoles] = useState<any[]>([]);

  const permissionTests: PermissionTest[] = [
    {
      permission: 'employee_access',
      name: 'Employee Access',
      description: 'View employee profiles and basic information',
      icon: <User className="w-4 h-4" />,
      category: 'Core',
      testFunction: async () => {
        const { data, error } = await supabase.from('employees').select('id, first_name, last_name').limit(1);
        return !error && data && data.length > 0;
      }
    },
    {
      permission: 'leave_access',
      name: 'Leave Access',
      description: 'Manage personal leave requests',
      icon: <Calendar className="w-4 h-4" />,
      category: 'Core',
      testFunction: async () => {
        const { data, error } = await supabase.from('leave_requests').select('id').limit(1);
        return !error;
      }
    },
    {
      permission: 'timesheet_access',
      name: 'Timesheet Access',
      description: 'Manage personal timesheets',
      icon: <Clock className="w-4 h-4" />,
      category: 'Core',
      testFunction: async () => {
        const { data, error } = await supabase.from('timesheet_entries').select('id').limit(1);
        return !error;
      }
    },
    {
      permission: 'document_access',
      name: 'Document Access',
      description: 'View and upload personal documents',
      icon: <FileText className="w-4 h-4" />,
      category: 'Core',
      testFunction: async () => {
        const { data, error } = await supabase.from('employee_documents').select('id').limit(1);
        return !error;
      }
    },
    {
      permission: 'performance_access',
      name: 'Performance Access',
      description: 'View personal performance data',
      icon: <TrendingUp className="w-4 h-4" />,
      category: 'Core',
      testFunction: async () => {
        const { data, error } = await supabase.from('performance_goals').select('id').limit(1);
        return !error;
      }
    },
    {
      permission: 'team_management',
      name: 'Team Management',
      description: 'Manage team members and data',
      icon: <Users className="w-4 h-4" />,
      category: 'Management',
      testFunction: async () => {
        const { data, error } = await supabase.from('employees').select('id').limit(5);
        return !error && data && data.length > 0;
      }
    },
    {
      permission: 'leave_approval',
      name: 'Leave Approval',
      description: 'Approve or reject team leave requests',
      icon: <Calendar className="w-4 h-4" />,
      category: 'Management',
      testFunction: async () => {
        const { data, error } = await supabase.from('leave_requests').select('id, status').limit(1);
        return !error;
      }
    },
    {
      permission: 'performance_review',
      name: 'Performance Review',
      description: 'Conduct performance reviews',
      icon: <TrendingUp className="w-4 h-4" />,
      category: 'Management',
      testFunction: async () => {
        const { data, error } = await supabase.from('performance_goals').select('id').limit(1);
        return !error;
      }
    },
    {
      permission: 'hr_management',
      name: 'HR Management',
      description: 'Full employee lifecycle management',
      icon: <Users className="w-4 h-4" />,
      category: 'HR',
      testFunction: async () => {
        const { data, error } = await supabase.from('employees').select('*').limit(1);
        return !error;
      }
    },
    {
      permission: 'payroll_management',
      name: 'Payroll Management',
      description: 'Process and manage payroll',
      icon: <DollarSign className="w-4 h-4" />,
      category: 'HR',
      testFunction: async () => {
        // Simulate payroll access test
        return true; // Placeholder since we don't have payroll tables yet
      }
    },
    {
      permission: 'role_management',
      name: 'Role Management',
      description: 'Manage roles and permissions',
      icon: <Shield className="w-4 h-4" />,
      category: 'Admin',
      testFunction: async () => {
        const { data, error } = await supabase.from('roles').select('id').limit(1);
        return !error;
      }
    },
    {
      permission: 'system_reports',
      name: 'System Reports',
      description: 'Generate and export reports',
      icon: <BarChart3 className="w-4 h-4" />,
      category: 'Admin',
      testFunction: async () => {
        const { data, error } = await supabase.from('employees').select('count').limit(1);
        return !error;
      }
    },
    {
      permission: 'admin_access',
      name: 'Admin Access',
      description: 'Full system administration',
      icon: <Settings className="w-4 h-4" />,
      category: 'Admin',
      testFunction: async () => {
        const { data, error } = await supabase.from('roles').select('*').limit(1);
        return !error;
      }
    }
  ];

  useEffect(() => {
    if (currentEmployee) {
      loadUserPermissions();
    }
  }, [currentEmployee]);

  const loadUserPermissions = async () => {
    try {
      // Get user's roles and permissions
      const { data: employeeRoles, error } = await supabase
        .from('employee_roles')
        .select(`
          role:roles(name, permissions)
        `)
        .eq('employee_id', currentEmployee?.id)
        .eq('is_active', true);

      if (!error && employeeRoles) {
        setUserRoles(employeeRoles);
        
        // Aggregate all permissions from all roles
        const allPermissions = new Set<string>();
        employeeRoles.forEach(er => {
          if (er.role && er.role.permissions) {
            er.role.permissions.forEach((perm: string) => allPermissions.add(perm));
          }
        });

        // Update permission state
        const permissions: UserPermissions = {
          employee_access: allPermissions.has('employee_access'),
          leave_access: allPermissions.has('leave_access'),
          timesheet_access: allPermissions.has('timesheet_access'),
          document_access: allPermissions.has('document_access'),
          performance_access: allPermissions.has('performance_access'),
          team_management: allPermissions.has('team_management'),
          leave_approval: allPermissions.has('leave_approval'),
          performance_review: allPermissions.has('performance_review'),
          document_approval: allPermissions.has('document_approval'),
          hr_management: allPermissions.has('hr_management'),
          payroll_management: allPermissions.has('payroll_management'),
          policy_management: allPermissions.has('policy_management'),
          role_management: allPermissions.has('role_management'),
          system_reports: allPermissions.has('system_reports'),
          admin_access: allPermissions.has('admin_access')
        };

        setUserPermissions(permissions);
      }
    } catch (error) {
      console.error('Error loading user permissions:', error);
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    const results: Record<string, boolean> = {};

    for (const test of permissionTests) {
      try {
        const result = await test.testFunction();
        results[test.permission] = result;
      } catch (error) {
        console.error(`Test failed for ${test.permission}:`, error);
        results[test.permission] = false;
      }
    }

    setTestResults(results);
    setTesting(false);
  };

  const getPermissionStatus = (permission: keyof UserPermissions) => {
    const hasPermission = userPermissions[permission];
    const testPassed = testResults[permission];
    
    if (hasPermission && testPassed) return 'success';
    if (hasPermission && testPassed === false) return 'warning';
    if (!hasPermission) return 'disabled';
    return 'unknown';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <Check className="w-4 h-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'disabled': return <X className="w-4 h-4 text-gray-400" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-50 border-green-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      case 'disabled': return 'bg-gray-50 border-gray-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const groupedTests = permissionTests.reduce((acc, test) => {
    if (!acc[test.category]) acc[test.category] = [];
    acc[test.category].push(test);
    return acc;
  }, {} as Record<string, PermissionTest[]>);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Role & Permission Tester</h1>
            <p className="text-gray-600">Test and verify your role permissions are working correctly</p>
          </div>
          <button
            onClick={runAllTests}
            disabled={testing}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Shield className="w-4 h-4" />
            <span>{testing ? 'Testing...' : 'Run All Tests'}</span>
          </button>
        </div>

        {/* User Roles Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-2">Your Current Roles</h3>
          <div className="flex flex-wrap gap-2">
            {userRoles.map((roleAssignment, index) => (
              <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                {roleAssignment.role?.name || 'Unknown Role'}
              </span>
            ))}
            {userRoles.length === 0 && (
              <span className="text-gray-500 text-sm">No roles assigned</span>
            )}
          </div>
        </div>
      </div>

      {/* Permission Tests by Category */}
      {Object.entries(groupedTests).map(([category, tests]) => (
        <div key={category} className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">{category} Permissions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tests.map((test) => {
              const status = getPermissionStatus(test.permission);
              return (
                <div key={test.permission} className={`border rounded-lg p-4 ${getStatusColor(status)}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {test.icon}
                      <h3 className="font-medium text-gray-900">{test.name}</h3>
                    </div>
                    {getStatusIcon(status)}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{test.description}</p>
                  <div className="flex items-center justify-between text-xs">
                    <span className={`px-2 py-1 rounded ${
                      userPermissions[test.permission] 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {userPermissions[test.permission] ? 'Granted' : 'Not Granted'}
                    </span>
                    {testResults[test.permission] !== undefined && (
                      <span className={`px-2 py-1 rounded ${
                        testResults[test.permission] 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {testResults[test.permission] ? 'Test Passed' : 'Test Failed'}
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}

      {/* Test Summary */}
      {Object.keys(testResults).length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Summary</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Object.values(testResults).filter(Boolean).length}
              </div>
              <div className="text-sm text-gray-600">Tests Passed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {Object.values(testResults).filter(r => !r).length}
              </div>
              <div className="text-sm text-gray-600">Tests Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Object.values(userPermissions).filter(Boolean).length}
              </div>
              <div className="text-sm text-gray-600">Permissions Granted</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {Object.values(userPermissions).filter(p => !p).length}
              </div>
              <div className="text-sm text-gray-600">Permissions Denied</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RolePermissionTester;
