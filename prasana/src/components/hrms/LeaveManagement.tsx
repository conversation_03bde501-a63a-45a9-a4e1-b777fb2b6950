import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Plus, 
  CheckCircle, 
  XCircle, 
  Clock,
  Filter,
  Search,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { supabase } from '../../supabaseClient';
import { hrmsService } from '../../services/hrmsService';
import {
  LeaveApplication,
  LeaveType,
  EmployeeLeaveBalance,
  Employee,
  LeaveApplicationFormData,
  LeaveFilters
} from '../../types/hrms';

interface LeaveManagementProps {
  employeeId?: string; // If not provided, shows current user's leaves
  isManager?: boolean; // If true, shows team leaves for approval
}

const LeaveManagement: React.FC<LeaveManagementProps> = ({ 
  employeeId, 
  isManager = false 
}) => {
  const [leaveApplications, setLeaveApplications] = useState<LeaveApplication[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [leaveBalance, setLeaveBalance] = useState<EmployeeLeaveBalance[]>([]);
  const [currentEmployee, setCurrentEmployee] = useState<Employee | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<LeaveApplication | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [filters, setFilters] = useState<LeaveFilters>({});
  const [formData, setFormData] = useState<LeaveApplicationFormData>({
    leave_type_id: '',
    start_date: '',
    end_date: '',
    reason: ''
  });

  useEffect(() => {
    loadLeaveData();
  }, [employeeId, isManager]);

  const loadLeaveData = async () => {
    try {
      setLoading(true);
      
      // Load current employee
      let employee: Employee | null;
      if (employeeId) {
        employee = await hrmsService.getEmployeeById(employeeId);
      } else {
        employee = await hrmsService.getCurrentEmployee();
      }
      setCurrentEmployee(employee);

      // Load leave types
      const types = await hrmsService.getLeaveTypes();
      setLeaveTypes(types);

      if (employee) {
        // Load leave applications from simplified table
        const { data: applications, error: appError } = await supabase
          .from('leave_applications')
          .select('*')
          .eq('employee_id', employee.id)
          .order('created_at', { ascending: false });

        if (!appError && applications) {
          // Convert to proper format
          const formattedApps = applications.map(app => ({
            ...app,
            leave_type: { name: app.leave_type, code: app.leave_type },
            employee: { first_name: employee.first_name, last_name: employee.last_name }
          }));
          setLeaveApplications(formattedApps as any);
        }

        // Load leave balance (only for individual employee view)
        if (!isManager) {
          const balance = await hrmsService.getLeaveBalance(employee.id);
          setLeaveBalance(balance);
        }
      }
    } catch (error) {
      console.error('Error loading leave data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitApplication = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentEmployee) return;

    try {
      setSubmitting(true);

      // Calculate total days
      const totalDays = calculateLeaveDays(formData.start_date, formData.end_date);

      // Simple leave application using text fields
      const { error } = await supabase
        .from('leave_applications')
        .insert([{
          employee_id: currentEmployee.id,
          leave_type: formData.leave_type_id, // Store as text
          start_date: formData.start_date,
          end_date: formData.end_date,
          total_days: totalDays.toString(),
          reason: formData.reason || '',
          status: 'pending',
          applied_date: new Date().toISOString().split('T')[0]
        }]);

      if (error) throw error;

      setShowApplicationForm(false);
      setFormData({
        leave_type_id: '',
        start_date: '',
        end_date: '',
        reason: ''
      });
      await loadLeaveData(); // Refresh data
    } catch (error) {
      console.error('Error submitting leave application:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const calculateLeaveDays = (startDate: string, endDate: string): number => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const timeDiff = end.getTime() - start.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-black gradient-text header-section animate-scaleIn">
            {isManager ? 'Team Leave Management' : 'My Leaves'}
          </h1>
          <p className="text-gray-600">
            {isManager ? 'Manage team leave requests' : 'Apply and track your leave applications'}
          </p>
        </div>
        {!isManager && (
          <button
            onClick={() => setShowApplicationForm(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Apply Leave</span>
          </button>
        )}
      </div>

      {/* Leave Balance (for individual employee view) */}
      {!isManager && leaveBalance.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Leave Balance</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {leaveBalance.map((balance) => (
              <div key={balance.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-gray-900">{balance.leave_type?.name}</h3>
                  <span className="text-2xl font-bold text-blue-600">{balance.available_days}</span>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex justify-between">
                    <span>Allocated:</span>
                    <span>{balance.allocated_days}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Used:</span>
                    <span>{balance.used_days}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Carried Forward:</span>
                    <span>{balance.carried_forward}</span>
                  </div>
                </div>
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ 
                        width: `${(balance.used_days / balance.allocated_days) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Leave Applications */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Leave Applications</h2>
            <div className="flex space-x-3">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search applications..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                <Filter className="w-4 h-4" />
                <span>Filter</span>
              </button>
            </div>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {leaveApplications.map((application) => (
            <div key={application.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(application.status)}
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {application.leave_type?.name}
                        {isManager && application.employee && (
                          <span className="ml-2 text-gray-600">
                            - {application.employee.first_name} {application.employee.last_name}
                          </span>
                        )}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {new Date(application.start_date).toLocaleDateString()} - {new Date(application.end_date).toLocaleDateString()}
                        <span className="ml-2">({application.total_days} days)</span>
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor(application.status)}`}>
                    {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                  </span>
                  <button
                    onClick={() => setSelectedApplication(application)}
                    className="text-blue-500 hover:text-blue-600"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                </div>
              </div>
              {application.reason && (
                <p className="mt-2 text-sm text-gray-600">
                  <span className="font-medium">Reason:</span> {application.reason}
                </p>
              )}
              <div className="mt-2 text-xs text-gray-500">
                Applied on {new Date(application.applied_date).toLocaleDateString()}
                {application.approved_by && application.approved_date && (
                  <span className="ml-4">
                    {application.status === 'approved' ? 'Approved' : 'Processed'} on {new Date(application.approved_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          ))}
          {leaveApplications.length === 0 && (
            <div className="p-8 text-center text-gray-500">
              No leave applications found
            </div>
          )}
        </div>
      </div>

      {/* Leave Application Form Modal */}
      {showApplicationForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Apply for Leave</h3>
            </div>
            <form onSubmit={handleSubmitApplication} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Leave Type
                </label>
                <select
                  value={formData.leave_type_id}
                  onChange={(e) => setFormData({ ...formData, leave_type_id: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select leave type</option>
                  {leaveTypes.map((type) => (
                    <option key={type.id} value={type.name}>
                      {type.name} ({type.code})
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
              </div>

              {formData.start_date && formData.end_date && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Total days: {calculateLeaveDays(formData.start_date, formData.end_date)}
                  </p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reason (Optional)
                </label>
                <textarea
                  value={formData.reason}
                  onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter reason for leave..."
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="submit"
                  disabled={submitting}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg disabled:opacity-50"
                >
                  {submitting ? 'Submitting...' : 'Submit Application'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowApplicationForm(false)}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Leave Application Detail Modal */}
      {selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Leave Application Details</h3>
                <button
                  onClick={() => setSelectedApplication(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Leave Type</label>
                  <p className="text-gray-900">{selectedApplication.leave_type?.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <span className={`inline-flex px-2 py-1 text-sm rounded-full ${getStatusColor(selectedApplication.status)}`}>
                    {selectedApplication.status.charAt(0).toUpperCase() + selectedApplication.status.slice(1)}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Start Date</label>
                  <p className="text-gray-900">{new Date(selectedApplication.start_date).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">End Date</label>
                  <p className="text-gray-900">{new Date(selectedApplication.end_date).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Total Days</label>
                  <p className="text-gray-900">{selectedApplication.total_days}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Applied Date</label>
                  <p className="text-gray-900">{new Date(selectedApplication.applied_date).toLocaleDateString()}</p>
                </div>
              </div>
              {selectedApplication.reason && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reason</label>
                  <p className="text-gray-900">{selectedApplication.reason}</p>
                </div>
              )}
              {selectedApplication.rejection_reason && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Rejection Reason</label>
                  <p className="text-red-600">{selectedApplication.rejection_reason}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeaveManagement;
