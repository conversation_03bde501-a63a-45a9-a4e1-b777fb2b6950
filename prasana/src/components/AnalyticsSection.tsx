import React, { useState, useEffect } from 'react';
import {
  Briefcase,
  Users,
  TrendingUp,
  Clock,
  Calendar,
  Target,
  Award,
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON> as Pie<PERSON><PERSON><PERSON>con,
  Line<PERSON>hart,
  Filter,
  Download,
  RefreshCw,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Project } from '../types';
import { teams } from '../data/teams';
import { timesheetService } from '../services/timesheetService';
import { TimeEntry } from '../types/timesheet';
import { supabase } from '../supabaseClient';
import '../styles/Analytics.css';

// Import recharts components
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart as RechartsLineChart,
  Line,
  Area,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart
} from 'recharts';

// Enhanced color schemes for professional charts
const COLORS = {
  primary: ['#3B82F6', '#1D4ED8', '#1E40AF', '#1E3A8A'],
  secondary: ['#10B981', '#059669', '#047857', '#065F46'],
  accent: ['#F59E0B', '#D97706', '#B45309', '#92400E'],
  neutral: ['#6B7280', '#4B5563', '#374151', '#1F2937'],
  status: ['#EF4444', '#F97316', '#EAB308', '#84CC16', '#22C55E'],
  gradient: ['#8B5CF6', '#A855F7', '#C084FC', '#DDD6FE']
};

const CHART_COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];

// Enhanced interfaces
interface CompanyTeamMember {
  name: string;
  role: string;
  designation?: string;
  avatar?: string;
  uuid?: string;
  team?: string;
}

interface DatabaseEmployee {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department: string;
  designation: string;
  profile_picture_url?: string;
  created_at: string;
}

interface AnalyticsData {
  timeEntries: TimeEntry[];
  employees: DatabaseEmployee[];
  teamProductivity: any[];
  projectTimeline: any[];
  memberPerformance: any[];
  weeklyTrends: any[];
  loading: boolean;
}

interface AnalyticsSectionProps {
  projects: Project[];
  currentPage: string;
}

interface FilterOptions {
  dateRange: 'week' | 'month' | 'quarter' | 'year';
  team: string;
  project: string;
}

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: React.ReactNode;
  color: string;
  description: string;
}

// Helper to flatten all team members from the teams data
const getAllCompanyTeamMembers = (): CompanyTeamMember[] => {
  const all: CompanyTeamMember[] = [];
  Object.values(teams).forEach(team => {
    // Cast team members to the new CompanyTeamMember type during extraction
    if (team.sdm) all.push(team.sdm as CompanyTeamMember);
    if (team.tdm) all.push(team.tdm as CompanyTeamMember);
    if (team.cxm) all.push(team.cxm as CompanyTeamMember);
    if (team.members && Array.isArray(team.members)) {
      team.members.forEach(m => all.push(m as CompanyTeamMember));
    }
  });
  // Filter out duplicates by uuid if available, otherwise by name
  const uniqueMembers = all.reduce((acc: CompanyTeamMember[], currentMember) => {
    const isDuplicate = acc.some(member => 
      (member.uuid && currentMember.uuid && member.uuid === currentMember.uuid) ||
      (!member.uuid && !currentMember.uuid && member.name === currentMember.name)
    );
    if (!isDuplicate) {
      acc.push(currentMember);
    }
    return acc;
  }, []);
  return uniqueMembers;
};

const AnalyticsSection: React.FC<AnalyticsSectionProps> = ({ projects, currentPage }) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    timeEntries: [],
    employees: [],
    teamProductivity: [],
    projectTimeline: [],
    memberPerformance: [],
    weeklyTrends: [],
    loading: true
  });

  const [filters, setFilters] = useState<FilterOptions>({
    dateRange: 'month',
    team: '',
    project: ''
  });

  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    overview: true,
    productivity: true,
    projects: true,
    team: true
  });

  const [error, setError] = useState<string | null>(null);

  const allCompanyMembers = getAllCompanyTeamMembers();

  useEffect(() => {
    loadAnalyticsData();
  }, [filters]);

  const loadAnalyticsData = async () => {
    try {
      setAnalyticsData(prev => ({ ...prev, loading: true }));
      setError(null);

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();

      switch (filters.dateRange) {
        case 'week':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Fetch real employee data from database
      const { data: employees, error: employeesError } = await supabase
        .from('employees')
        .select('*')
        .order('created_at', { ascending: false });

      if (employeesError) {
        console.error('Error fetching employees:', employeesError);
        setError('Failed to fetch employee data. Using fallback data.');
      }

      // Fetch timesheet data
      const timeEntries = await timesheetService.getAllTimeEntries(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );

      // Use real employee data for analytics, fallback to mock data if needed
      const realEmployees = employees || [];
      const processedData = processAnalyticsData(timeEntries, projects, realEmployees);

      setAnalyticsData({
        timeEntries,
        employees: realEmployees,
        ...processedData,
        loading: false
      });

      // Show success message if we have real data
      if (employees && employees.length > 0) {
        console.log(`Analytics loaded with ${employees.length} employees and ${timeEntries.length} timesheet entries`);
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
      setError('Failed to load analytics data. Please try again.');
      setAnalyticsData(prev => ({ ...prev, loading: false }));
    }
  };

  const processAnalyticsData = (timeEntries: TimeEntry[], projects: Project[], employees: DatabaseEmployee[]) => {
    // Convert database employees to member format for compatibility
    const members = employees.map(emp => ({
      name: `${emp.first_name} ${emp.last_name}`,
      role: emp.designation || 'Employee',
      designation: emp.designation,
      avatar: emp.profile_picture_url,
      uuid: emp.user_id,
      team: emp.department
    }));

    // Team productivity analysis
    const teamProductivity = calculateTeamProductivity(timeEntries, members);

    // Project timeline analysis
    const projectTimeline = calculateProjectTimeline(timeEntries, projects);

    // Member performance analysis
    const memberPerformance = calculateMemberPerformance(timeEntries, members);

    // Weekly trends analysis
    const weeklyTrends = calculateWeeklyTrends(timeEntries);

    return {
      teamProductivity,
      projectTimeline,
      memberPerformance,
      weeklyTrends
    };
  };

  // Data processing functions
  const calculateTeamProductivity = (timeEntries: TimeEntry[], members: CompanyTeamMember[]) => {
    const teamHours: {[key: string]: number} = {};
    const teamMembers: {[key: string]: number} = {};

    // Group members by team
    members.forEach(member => {
      if (member.team) {
        teamMembers[member.team] = (teamMembers[member.team] || 0) + 1;
      }
    });

    // Calculate hours per team
    timeEntries.forEach(entry => {
      const member = members.find(m => m.uuid === entry.user_id);
      if (member && member.team) {
        teamHours[member.team] = (teamHours[member.team] || 0) + (entry.duration || 0);
      }
    });

    return Object.entries(teamHours).map(([team, hours]) => ({
      team,
      hours: Math.round(hours / 60 * 10) / 10, // Convert to hours with 1 decimal
      members: teamMembers[team] || 0,
      avgHoursPerMember: teamMembers[team] ? Math.round((hours / 60) / teamMembers[team] * 10) / 10 : 0
    }));
  };

  const calculateProjectTimeline = (timeEntries: TimeEntry[], projects: Project[]) => {
    const projectHours: {[key: string]: number} = {};

    timeEntries.forEach(entry => {
      if (entry.project_name_text) {
        projectHours[entry.project_name_text] = (projectHours[entry.project_name_text] || 0) + (entry.duration || 0);
      }
    });

    return Object.entries(projectHours)
      .map(([project, hours]) => ({
        project,
        hours: Math.round(hours / 60 * 10) / 10,
        entries: timeEntries.filter(e => e.project_name_text === project).length
      }))
      .sort((a, b) => b.hours - a.hours)
      .slice(0, 10); // Top 10 projects
  };

  const calculateMemberPerformance = (timeEntries: TimeEntry[], members: CompanyTeamMember[]) => {
    const memberHours: {[key: string]: number} = {};
    const memberEntries: {[key: string]: number} = {};

    timeEntries.forEach(entry => {
      if (entry.user_id) {
        memberHours[entry.user_id] = (memberHours[entry.user_id] || 0) + (entry.duration || 0);
        memberEntries[entry.user_id] = (memberEntries[entry.user_id] || 0) + 1;
      }
    });

    return members
      .map(member => ({
        name: member.name,
        team: member.team || 'Unknown',
        hours: Math.round((memberHours[member.uuid || ''] || 0) / 60 * 10) / 10,
        entries: memberEntries[member.uuid || ''] || 0,
        avgHoursPerEntry: memberEntries[member.uuid || '']
          ? Math.round(((memberHours[member.uuid || ''] || 0) / 60) / memberEntries[member.uuid || ''] * 10) / 10
          : 0
      }))
      .filter(member => member.hours > 0)
      .sort((a, b) => b.hours - a.hours);
  };

  const calculateWeeklyTrends = (timeEntries: TimeEntry[]) => {
    const weeklyData: {[key: string]: number} = {};

    timeEntries.forEach(entry => {
      if (entry.entry_date) {
        const date = new Date(entry.entry_date);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
        const weekKey = weekStart.toISOString().split('T')[0];

        weeklyData[weekKey] = (weeklyData[weekKey] || 0) + (entry.duration || 0);
      }
    });

    return Object.entries(weeklyData)
      .map(([week, hours]) => ({
        week: new Date(week).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        hours: Math.round(hours / 60 * 10) / 10,
        entries: timeEntries.filter(e => {
          if (!e.entry_date) return false;
          const entryDate = new Date(e.entry_date);
          const weekStart = new Date(entryDate);
          weekStart.setDate(entryDate.getDate() - entryDate.getDay());
          return weekStart.toISOString().split('T')[0] === week;
        }).length
      }))
      .sort((a, b) => new Date(a.week).getTime() - new Date(b.week).getTime());
  };

  // Calculate metrics using real data
  const totalProjects = projects.length;
  const totalMembers = analyticsData.employees.length || allCompanyMembers.length; // Fallback to mock data if no real data
  const totalHours = analyticsData.timeEntries.reduce((sum, entry) => sum + (entry.duration || 0), 0) / 60;
  const totalEntries = analyticsData.timeEntries.length;
  const avgHoursPerMember = totalMembers > 0 ? totalHours / totalMembers : 0;
  const avgHoursPerProject = totalProjects > 0 ? totalHours / totalProjects : 0;

  // Calculate active members (those with timesheet entries)
  const activeMembers = new Set(analyticsData.timeEntries.map(entry => entry.user_id)).size;

  // Calculate project and team data for charts
  const teamStats = projects.reduce((acc: { [key: string]: number }, project) => {
    acc[project.team] = (acc[project.team] || 0) + 1;
    return acc;
  }, {});
  const teamData = Object.entries(teamStats).map(([name, value]) => ({ name, value }));

  const categoryStats = projects.reduce((acc: { [key: string]: number }, project) => {
    acc[project.clientCategory] = (acc[project.clientCategory] || 0) + 1;
    return acc;
  }, {});
  const categoryData = Object.entries(categoryStats).map(([name, value]) => ({ name, value }));

  const metricCards: MetricCard[] = [
    {
      title: 'Total Projects',
      value: totalProjects,
      change: 12,
      changeType: 'increase',
      icon: <Briefcase className="w-6 h-6" />,
      color: 'blue',
      description: 'Active and completed projects'
    },
    {
      title: 'Active Members',
      value: `${activeMembers}/${totalMembers}`,
      change: 5,
      changeType: 'increase',
      icon: <Users className="w-6 h-6" />,
      color: 'green',
      description: 'Members with logged hours'
    },
    {
      title: 'Total Hours',
      value: `${totalHours.toFixed(1)}h`,
      change: 8,
      changeType: 'increase',
      icon: <Clock className="w-6 h-6" />,
      color: 'purple',
      description: `From ${totalEntries} timesheet entries`
    },
    {
      title: 'Avg Hours/Member',
      value: `${avgHoursPerMember.toFixed(1)}h`,
      change: activeMembers > 0 ? 15 : -2,
      changeType: activeMembers > 0 ? 'increase' : 'decrease',
      icon: <Target className="w-6 h-6" />,
      color: 'orange',
      description: 'Average productivity per member'
    }
  ];
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const exportData = () => {
    // Implementation for data export
    console.log('Exporting analytics data...');
  };

  return (
    <div className="analytics-dashboard min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-4 lg:mb-0">
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                Analytics Dashboard
              </h1>
              <p className="text-gray-600 mt-2">
                Comprehensive insights into team performance and project analytics
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              {/* Filters */}
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                >
                  <option value="week">Last Week</option>
                  <option value="month">Last Month</option>
                  <option value="quarter">Last Quarter</option>
                  <option value="year">Last Year</option>
                </select>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={loadAnalyticsData}
                  className="interactive-button flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                  disabled={analyticsData.loading}
                >
                  <RefreshCw className={`h-4 w-4 ${analyticsData.loading ? 'loading-spinner' : ''}`} />
                  Refresh
                </button>

                <button
                  onClick={exportData}
                  className="interactive-button flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  Export
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Data Status Indicator */}
        {!analyticsData.loading && !error && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-800">
                  Analytics loaded: {analyticsData.employees.length} employees, {analyticsData.timeEntries.length} timesheet entries
                  {analyticsData.employees.length === 0 && " (using sample data)"}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {analyticsData.loading && (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}

        {!analyticsData.loading && (
          <>
            {/* Metrics Overview */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Key Metrics</h2>
                <button
                  onClick={() => toggleSection('overview')}
                  className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                >
                  {expandedSections.overview ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>
              </div>

              {expandedSections.overview && (
                <div className="metric-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {metricCards.map((metric, index) => (
                    <div key={index} className="metric-card bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`p-3 rounded-lg ${
                          metric.color === 'blue' ? 'bg-blue-100' :
                          metric.color === 'green' ? 'bg-green-100' :
                          metric.color === 'purple' ? 'bg-purple-100' :
                          metric.color === 'orange' ? 'bg-orange-100' :
                          'bg-gray-100'
                        }`}>
                          <div className={`${
                            metric.color === 'blue' ? 'text-blue-600' :
                            metric.color === 'green' ? 'text-green-600' :
                            metric.color === 'purple' ? 'text-purple-600' :
                            metric.color === 'orange' ? 'text-orange-600' :
                            'text-gray-600'
                          }`}>
                            {metric.icon}
                          </div>
                        </div>
                        <div className={`flex items-center text-sm font-medium ${
                          metric.changeType === 'increase' ? 'text-green-600' :
                          metric.changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          <TrendingUp className={`h-4 w-4 mr-1 ${
                            metric.changeType === 'decrease' ? 'rotate-180' : ''
                          }`} />
                          {Math.abs(metric.change)}%
                        </div>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</p>
                        <p className="text-sm text-gray-600">{metric.title}</p>
                        <p className="text-xs text-gray-500 mt-1">{metric.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Team Productivity Analysis */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Team Productivity</h2>
                <button
                  onClick={() => toggleSection('productivity')}
                  className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                >
                  {expandedSections.productivity ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>
              </div>

              {expandedSections.productivity && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Team Hours Chart */}
                  <div className="chart-container bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Hours by Team</h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={analyticsData.teamProductivity}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="team"
                            tick={{ fontSize: 12 }}
                            stroke="#6b7280"
                          />
                          <YAxis
                            tick={{ fontSize: 12 }}
                            stroke="#6b7280"
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: '#fff',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                          />
                          <Bar
                            dataKey="hours"
                            fill="#3B82F6"
                            radius={[4, 4, 0, 0]}
                            name="Hours"
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>

                  {/* Weekly Trends */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Trends</h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={analyticsData.weeklyTrends}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="week"
                            tick={{ fontSize: 12 }}
                            stroke="#6b7280"
                          />
                          <YAxis
                            tick={{ fontSize: 12 }}
                            stroke="#6b7280"
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: '#fff',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                          />
                          <Area
                            type="monotone"
                            dataKey="hours"
                            stroke="#10B981"
                            fill="#10B981"
                            fillOpacity={0.3}
                            strokeWidth={2}
                            name="Hours"
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Project Analytics */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Project Analytics</h2>
                <button
                  onClick={() => toggleSection('projects')}
                  className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                >
                  {expandedSections.projects ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>
              </div>

              {expandedSections.projects && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Projects by Team */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Projects by Team</h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={teamData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {teamData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </div>

                  {/* Projects by Category */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Projects by Category</h3>
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={categoryData}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="name"
                            tick={{ fontSize: 12 }}
                            stroke="#6b7280"
                          />
                          <YAxis
                            tick={{ fontSize: 12 }}
                            stroke="#6b7280"
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: '#fff',
                              border: '1px solid #e5e7eb',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                          />
                          <Bar
                            dataKey="value"
                            fill="#F59E0B"
                            radius={[4, 4, 0, 0]}
                            name="Projects"
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Team Performance */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Team Performance</h2>
                <button
                  onClick={() => toggleSection('team')}
                  className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                >
                  {expandedSections.team ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>
              </div>

              {expandedSections.team && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Top Performers */}
                  <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performers</h3>
                    <div className="space-y-4">
                      {analyticsData.memberPerformance.slice(0, 10).map((member, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold text-sm ${
                              index === 0 ? 'bg-yellow-500' :
                              index === 1 ? 'bg-gray-400' :
                              index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                            }`}>
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{member.name}</p>
                              <p className="text-sm text-gray-500">{member.team}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">{member.hours}h</p>
                            <p className="text-sm text-gray-500">{member.entries} entries</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Team Statistics */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Team Stats</h3>
                    <div className="space-y-4">
                      {analyticsData.teamProductivity.map((team, index) => (
                        <div key={index} className="border-b border-gray-200 pb-3 last:border-b-0">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-gray-900">{team.team}</span>
                            <span className="text-sm text-gray-500">{team.members} members</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Total Hours</span>
                            <span className="font-semibold text-blue-600">{team.hours}h</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Avg/Member</span>
                            <span className="font-semibold text-green-600">{team.avgHoursPerMember}h</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Project Timeline */}
            <div className="mb-8">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Project Time Distribution</h3>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={analyticsData.projectTimeline}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="project"
                        tick={{ fontSize: 12 }}
                        stroke="#6b7280"
                        angle={-45}
                        textAnchor="end"
                        height={100}
                      />
                      <YAxis
                        yAxisId="hours"
                        tick={{ fontSize: 12 }}
                        stroke="#6b7280"
                      />
                      <YAxis
                        yAxisId="entries"
                        orientation="right"
                        tick={{ fontSize: 12 }}
                        stroke="#6b7280"
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#fff',
                          border: '1px solid #e5e7eb',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Bar
                        yAxisId="hours"
                        dataKey="hours"
                        fill="#3B82F6"
                        radius={[4, 4, 0, 0]}
                        name="Hours"
                      />
                      <Line
                        yAxisId="entries"
                        type="monotone"
                        dataKey="entries"
                        stroke="#EF4444"
                        strokeWidth={2}
                        name="Entries"
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* Insights and Recommendations */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-600" />
                Insights & Recommendations
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-900">Productivity Trend</span>
                  </div>
                  <p className="text-sm text-blue-800">
                    Team productivity has increased by 12% compared to the previous period.
                  </p>
                </div>

                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Award className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-900">Top Team</span>
                  </div>
                  <p className="text-sm text-green-800">
                    {analyticsData.teamProductivity[0]?.team || 'N/A'} team leads with highest productivity.
                  </p>
                </div>

                <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4 text-orange-600" />
                    <span className="font-medium text-orange-900">Focus Area</span>
                  </div>
                  <p className="text-sm text-orange-800">
                    Consider optimizing time allocation for better project distribution.
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AnalyticsSection;