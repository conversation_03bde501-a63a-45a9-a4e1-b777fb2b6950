import React, { useState, useRef, useEffect } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Crown, 
  Users, 
  Mail, 
  Phone,
  ZoomIn,
  ZoomOut,
  Maximize,
  Download
} from 'lucide-react';
import { OrganizationNode } from '../../types/teamDirectory';
import { DEPARTMENT_COLORS } from '../../types/teamDirectory';

interface OrganizationalChartProps {
  nodes: OrganizationNode[];
  onEmployeeClick: (employee: any) => void;
  searchQuery?: string;
}

const OrganizationalChart: React.FC<OrganizationalChartProps> = ({
  nodes,
  onEmployeeClick,
  searchQuery = ''
}) => {
  console.log('🎯 OrganizationalChart received nodes:', nodes);
  console.log('🎯 Number of nodes:', nodes.length);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isPanning, setIsPanning] = useState(false);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate expanded SDMs count for dynamic spacing
  const getExpandedSDMsCount = () => {
    return nodes.reduce((count, node) => {
      return count + node.children.filter(child => expandedNodes.has(child.id)).length;
    }, 0);
  };

  // Check if we should use vertical layout to prevent overlap
  const shouldUseVerticalLayout = () => {
    const expandedCount = getExpandedSDMsCount();
    const totalChildrenInExpanded = nodes.reduce((total, node) => {
      const expandedChildren = node.children.filter(child => expandedNodes.has(child.id));
      return total + expandedChildren.reduce((childTotal, child) => childTotal + child.children.length, 0);
    }, 0);

    // Use vertical layout immediately when any SDM is expanded to prevent overlap
    return expandedCount > 0 || totalChildrenInExpanded > 0;
  };

  // Calculate dynamic spacing based on content density
  const getDynamicSpacing = () => {
    const expandedCount = getExpandedSDMsCount();
    const isVertical = shouldUseVerticalLayout();

    if (isVertical) {
      return {
        containerGap: '6rem',
        itemMargin: '4rem',
        containerPadding: '3rem 0'
      };
    } else {
      return {
        containerGap: '3rem',
        itemMargin: '2rem',
        containerPadding: '2rem 0'
      };
    }
  };

  // Auto-expand nodes based on hierarchy level
  useEffect(() => {
    const autoExpanded = new Set<string>();
    const addAutoExpanded = (nodeList: OrganizationNode[]) => {
      nodeList.forEach(node => {
        if (node.level <= 2) { // Auto-expand CEO, CRO, SDM levels
          autoExpanded.add(node.id);
        }
        addAutoExpanded(node.children);
      });
    };
    addAutoExpanded(nodes);
    setExpandedNodes(autoExpanded);
  }, [nodes]);

  const toggleNodeExpansion = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.1, 0.5));
  };

  const handleResetView = () => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) { // Left mouse button
      setIsPanning(true);
      setDragStart({ x: e.clientX - panOffset.x, y: e.clientY - panOffset.y });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isPanning) {
      setPanOffset({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsPanning(false);
  };

  const isNodeHighlighted = (node: OrganizationNode): boolean => {
    if (!searchQuery) return false;
    const query = searchQuery.toLowerCase();
    return (
      node.name.toLowerCase().includes(query) ||
      node.designation.toLowerCase().includes(query) ||
      node.department.toLowerCase().includes(query) ||
      node.email?.toLowerCase().includes(query) ||
      false
    );
  };

  const renderNode = (node: OrganizationNode, level: number = 0): React.ReactNode => {
    const hasChildren = node.children.length > 0;
    const isExpanded = expandedNodes.has(node.id);
    const isHighlighted = isNodeHighlighted(node);
    const departmentColor = DEPARTMENT_COLORS[node.department as keyof typeof DEPARTMENT_COLORS] || '#6B7280';

    return (
      <div key={node.id} className="org-chart-node-wrapper">
        {/* Node Card */}
        <div
          className={`org-chart-node relative bg-white rounded-lg shadow-md border-2 transition-all duration-200 cursor-pointer hover:shadow-lg ${
            isHighlighted
              ? 'border-yellow-400 bg-yellow-50 shadow-lg'
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => onEmployeeClick(node.employeeData)}
          style={{
            minWidth: '200px',
            maxWidth: '250px',
            position: 'relative',
            zIndex: 10
          }}
        >
          {/* Department Color Bar */}
          <div 
            className="h-1 w-full rounded-t-lg"
            style={{ backgroundColor: departmentColor }}
          />
          
          <div className="p-4">
            {/* Profile Section */}
            <div className="flex items-center space-x-3 mb-3">
              <div className="relative">
                {node.avatar ? (
                  <img 
                    src={node.avatar} 
                    alt={node.name}
                    className="w-12 h-12 rounded-full object-cover border-2 border-gray-100"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <div 
                  className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold border-2 border-gray-100 ${node.avatar ? 'hidden' : ''}`}
                  style={{ backgroundColor: departmentColor }}
                >
                  {node.name.split(' ').map(n => n.charAt(0)).join('').slice(0, 2)}
                </div>
                
                {/* Leadership Badge */}
                {node.level <= 2 && (
                  <div className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-1">
                    <Crown className="w-3 h-3 text-yellow-800" />
                  </div>
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-gray-900 text-sm truncate">
                  {node.name}
                </h4>
                <p className="text-xs text-gray-600 truncate">
                  {node.designation}
                </p>
                <div className="flex items-center space-x-1 mt-1">
                  <span 
                    className="inline-block w-2 h-2 rounded-full"
                    style={{ backgroundColor: departmentColor }}
                  />
                  <span className="text-xs text-gray-500 truncate">
                    {node.department}
                  </span>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-1 mb-3">
              {node.email && (
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <Mail className="w-3 h-3 flex-shrink-0" />
                  <span className="truncate">{node.email}</span>
                </div>
              )}
              {node.phone && (
                <div className="flex items-center space-x-2 text-xs text-gray-600">
                  <Phone className="w-3 h-3 flex-shrink-0" />
                  <span className="truncate">{node.phone}</span>
                </div>
              )}
            </div>

            {/* Team Size */}
            {hasChildren && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Users className="w-3 h-3" />
                  <span>{node.children.length} direct report{node.children.length !== 1 ? 's' : ''}</span>
                </div>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleNodeExpansion(node.id);
                  }}
                  className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                >
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Connection Line and Children */}
        {hasChildren && isExpanded && (
          <div className="org-chart-level mt-4">
            {/* Vertical line */}
            <div className="w-px h-6 bg-gray-300" />

            {/* Horizontal line container */}
            <div className="relative w-full flex justify-center">
              <div
                className="h-px bg-gray-300 relative"
                style={{
                  width: `${Math.min(node.children.length * 280, Math.max(600, node.children.length * 200))}px`,
                  maxWidth: '1200px'
                }}
              >
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-px h-6 bg-gray-300" />
              </div>
            </div>

            {/* Children Container with Dynamic Layout */}
            {level === 1 ? (
              // Special layout for SDMs to prevent overlap
              <div
                className={`org-chart-sdm-container ${shouldUseVerticalLayout() ? 'vertical-layout' : ''}`}
                style={{
                  display: 'flex',
                  flexDirection: shouldUseVerticalLayout() ? 'column' : 'row',
                  flexWrap: shouldUseVerticalLayout() ? 'nowrap' : 'wrap',
                  justifyContent: 'center',
                  gap: getDynamicSpacing().containerGap,
                  alignItems: shouldUseVerticalLayout() ? 'center' : 'flex-start',
                  minHeight: 'fit-content',
                  width: '100%',
                  maxWidth: shouldUseVerticalLayout() ? '900px' : '1400px',
                  margin: '0 auto',
                  padding: getDynamicSpacing().containerPadding
                }}
              >
                {node.children.map((child, index) => (
                  <div
                    key={child.id}
                    className={`org-chart-sdm-item ${shouldUseVerticalLayout() ? 'full-width' : ''}`}
                    style={{
                      flex: shouldUseVerticalLayout() ? '1 1 100%' : '1 1 280px',
                      maxWidth: shouldUseVerticalLayout() ? '800px' : '350px',
                      minWidth: '250px',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      marginBottom: getDynamicSpacing().itemMargin,
                      zIndex: shouldUseVerticalLayout() ? 10 - index : 'auto',
                      position: 'relative'
                    }}
                  >
                    {renderNode(child, level + 1)}
                  </div>
                ))}
              </div>
            ) : (
              // Standard flex layout for other levels
              <div
                className={`org-chart-children-container level-${level + 1}`}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'center',
                  alignItems: 'flex-start',
                  gap: level === 2 ? '3rem' : '2rem',
                  position: 'relative',
                  width: '100%',
                  marginTop: '3rem',
                  marginBottom: '3rem',
                  padding: '2rem 1rem',
                  isolation: 'isolate',
                  zIndex: 10
                }}
              >
                {node.children.map((child, index) => (
                  <div
                    key={child.id}
                    style={{
                      position: 'relative',
                      zIndex: 10 - index,
                      flex: '0 0 auto'
                    }}
                  >
                    {renderNode(child, level + 1)}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  console.log('🎯 OrganizationalChart render check - nodes.length:', nodes.length);

  if (nodes.length === 0) {
    console.log('🎯 No nodes available, showing empty state');
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Users className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No organizational data found</h3>
        <p className="text-gray-500">The organizational chart will appear here once employee data is available.</p>
      </div>
    );
  }

  console.log('🎯 Rendering organization chart with', nodes.length, 'root nodes');

  return (
    <div className="relative bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Controls */}
      <div className="absolute top-4 right-4 z-10 flex items-center space-x-2">
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-1 flex items-center space-x-1">
          <button
            onClick={handleZoomOut}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Zoom Out"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          
          <span className="text-sm text-gray-600 px-2 min-w-[3rem] text-center">
            {Math.round(zoomLevel * 100)}%
          </span>
          
          <button
            onClick={handleZoomIn}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Zoom In"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          
          <div className="w-px h-6 bg-gray-300 mx-1" />
          
          <button
            onClick={handleResetView}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Reset View"
          >
            <Maximize className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Chart Container */}
      <div
        ref={containerRef}
        className="org-chart-container w-full overflow-hidden cursor-grab active:cursor-grabbing"
        style={{
          height: shouldUseVerticalLayout() ? 'auto' : '800px',
          minHeight: shouldUseVerticalLayout() ? '1000px' : '800px',
          maxHeight: shouldUseVerticalLayout() ? 'none' : '800px'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div
          className="w-full p-8 flex justify-center"
          style={{
            transform: `scale(${zoomLevel}) translate(${panOffset.x}px, ${panOffset.y}px)`,
            transformOrigin: 'center top',
            transition: isPanning ? 'none' : 'transform 0.2s ease-out',
            minHeight: shouldUseVerticalLayout() ? 'fit-content' : '100%',
            paddingBottom: shouldUseVerticalLayout() ? '4rem' : '2rem'
          }}
        >
          <div className="org-chart-level w-full max-w-7xl">
            {nodes.map((node) => renderNode(node))}
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-md border border-gray-200 p-3">
        <div className="text-xs text-gray-600 space-y-1">
          <div>• Click and drag to pan</div>
          <div>• Use zoom controls to scale</div>
          <div>• Click employee cards for details</div>
          <div>• Click arrows to expand/collapse teams</div>
          {shouldUseVerticalLayout() && (
            <div className="text-blue-600 font-medium mt-2 pt-2 border-t border-gray-200">
              📋 Vertical layout active to prevent overlap
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrganizationalChart;
