import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Crown, Shield, Star, Users } from 'lucide-react';
import { TeamMember } from '../../types/team';
import ProfileAvatar from '../ProfileAvatar';

interface HierarchicalOrgChartProps {
  employees: TeamMember[];
  onEmployeeClick: (employee: TeamMember) => void;
}

interface HierarchyNode {
  employee: TeamMember;
  level: number;
  children: HierarchyNode[];
  isExpanded: boolean;
}

const HierarchicalOrgChart: React.FC<HierarchicalOrgChartProps> = ({ employees, onEmployeeClick }) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['ceo', 'cro']));

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  // Build hierarchy tree
  const buildHierarchy = (): HierarchyNode | null => {
    // Find CEO (Level 1)
    const ceo = employees.find(emp => emp.designation === 'CEO');
    if (!ceo) return null;

    // Find CRO (Level 2)
    const cro = employees.find(emp => emp.designation === 'Chief Revenue Officer');
    
    // Find SDMs (Level 3) - they report to CRO
    const sdms = employees.filter(emp => emp.designation === 'Service Delivery Manager');
    
    // Find TDMs and CXMs (Level 4) - they report to SDMs
    const tdms = employees.filter(emp => emp.designation === 'Technical Delivery Manager');
    const cxms = employees.filter(emp => emp.designation === 'Client Experience Manager');
    
    // Find team members (Level 5)
    const teamMembers = employees.filter(emp => 
      emp.designation === 'Associate Trainee' || 
      (!['CEO', 'Chief Revenue Officer', 'Service Delivery Manager', 'Technical Delivery Manager', 'Client Experience Manager'].includes(emp.designation || ''))
    );

    // Build CRO node with SDMs as children
    const croChildren: HierarchyNode[] = [];
    
    // Add SDMs and their teams
    sdms.forEach(sdm => {
      const sdmTeamTdms = tdms.filter(tdm => tdm.team === sdm.team);
      const sdmTeamCxms = cxms.filter(cxm => cxm.team === sdm.team);
      const sdmTeamMembers = teamMembers.filter(member => member.team === sdm.team);
      
      const sdmChildren: HierarchyNode[] = [];
      
      // Add TDMs and CXMs for this SDM
      [...sdmTeamTdms, ...sdmTeamCxms].forEach(manager => {
        // For now, distribute team members evenly among TDMs/CXMs
        // In a real system, this would be based on actual reporting relationships
        const totalManagers = sdmTeamTdms.length + sdmTeamCxms.length;
        const managersArray = [...sdmTeamTdms, ...sdmTeamCxms];
        const managerIndex = managersArray.indexOf(manager);

        const managerTeamMembers = sdmTeamMembers.filter((member, index) => {
          // Distribute members among managers
          return totalManagers > 0 ? (index % totalManagers) === managerIndex : false;
        });

        const managerChildren: HierarchyNode[] = managerTeamMembers.map(member => ({
          employee: member,
          level: 5,
          children: [],
          isExpanded: false
        }));

        sdmChildren.push({
          employee: manager,
          level: 4,
          children: managerChildren,
          isExpanded: expandedNodes.has(manager.uuid || manager.id || '')
        });
      });

      // If there are team members but no TDMs/CXMs, add them directly under SDM
      if (sdmTeamTdms.length === 0 && sdmTeamCxms.length === 0 && sdmTeamMembers.length > 0) {
        const directReports: HierarchyNode[] = sdmTeamMembers.map(member => ({
          employee: member,
          level: 5,
          children: [],
          isExpanded: false
        }));

        sdmChildren.push(...directReports);
      }
      
      croChildren.push({
        employee: sdm,
        level: 3,
        children: sdmChildren,
        isExpanded: expandedNodes.has(sdm.uuid || sdm.id || '')
      });
    });
    
    // Add DEVELOPMENT TDM directly under CRO
    const developmentTdm = tdms.find(tdm => tdm.team === 'DEVELOPMENT');
    if (developmentTdm) {
      const developmentMembers = teamMembers.filter(member => member.team === 'DEVELOPMENT');
      const developmentChildren: HierarchyNode[] = developmentMembers.map(member => ({
        employee: member,
        level: 5,
        children: [],
        isExpanded: false
      }));
      
      croChildren.push({
        employee: developmentTdm,
        level: 4,
        children: developmentChildren,
        isExpanded: expandedNodes.has(developmentTdm.uuid || developmentTdm.id || '')
      });
    }

    // Build CRO node
    const croNode: HierarchyNode | undefined = cro ? {
      employee: cro,
      level: 2,
      children: croChildren,
      isExpanded: expandedNodes.has('cro')
    } : undefined;

    // Build CEO node (root)
    const ceoNode: HierarchyNode = {
      employee: ceo,
      level: 1,
      children: croNode ? [croNode] : croChildren, // If no CRO, SDMs report directly to CEO
      isExpanded: expandedNodes.has('ceo')
    };

    return ceoNode;
  };

  const getNodeIcon = (employee: TeamMember) => {
    switch (employee.designation) {
      case 'CEO':
        return <Crown className="w-4 h-4 text-yellow-600" />;
      case 'Chief Revenue Officer':
        return <Crown className="w-4 h-4 text-blue-600" />;
      case 'Service Delivery Manager':
        return <Shield className="w-4 h-4 text-green-600" />;
      case 'Technical Delivery Manager':
        return <Shield className="w-4 h-4 text-purple-600" />;
      case 'Client Experience Manager':
        return <Star className="w-4 h-4 text-orange-600" />;
      default:
        return <Users className="w-4 h-4 text-gray-600" />;
    }
  };

  const getNodeColor = (employee: TeamMember) => {
    switch (employee.designation) {
      case 'CEO':
        return 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100';
      case 'Chief Revenue Officer':
        return 'bg-blue-50 border-blue-200 hover:bg-blue-100';
      case 'Service Delivery Manager':
        return 'bg-green-50 border-green-200 hover:bg-green-100';
      case 'Technical Delivery Manager':
        return 'bg-purple-50 border-purple-200 hover:bg-purple-100';
      case 'Client Experience Manager':
        return 'bg-orange-50 border-orange-200 hover:bg-orange-100';
      default:
        return 'bg-gray-50 border-gray-200 hover:bg-gray-100';
    }
  };

  const renderNode = (node: HierarchyNode, isLast: boolean = false): React.ReactNode => {
    const hasChildren = node.children.length > 0;
    const nodeId = node.employee.uuid || node.employee.id || '';
    
    return (
      <div key={nodeId} className="relative">
        {/* Node Card */}
        <div className="flex items-start mb-4">
          {/* Expand/Collapse Button */}
          {hasChildren && (
            <button
              onClick={() => toggleNode(nodeId)}
              className="w-6 h-6 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors z-10 mr-3 mt-4"
            >
              {node.isExpanded ? (
                <ChevronDown className="w-3 h-3 text-gray-600" />
              ) : (
                <ChevronRight className="w-3 h-3 text-gray-600" />
              )}
            </button>
          )}

          {!hasChildren && (
            <div className="w-6 mr-3"></div>
          )}

          {/* Employee Card */}
          <div
            onClick={() => onEmployeeClick(node.employee)}
            className={`flex-1 min-w-80 p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${getNodeColor(node.employee)}`}
          >
            <div className="flex items-center space-x-3">
              <ProfileAvatar
                name={node.employee.name}
                avatar={node.employee.avatar}
                size="md"
                team={node.employee.team as any}
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  {getNodeIcon(node.employee)}
                  <h3 className="font-bold text-gray-900">{node.employee.name}</h3>
                </div>
                <p className="text-sm font-medium text-gray-700">{node.employee.designation}</p>
                <p className="text-xs text-gray-500">{node.employee.team} Team</p>
                {node.employee.employeeCode && (
                  <p className="text-xs text-blue-600 font-medium">PS: {node.employee.employeeCode}</p>
                )}
              </div>
              {hasChildren && (
                <div className="text-xs bg-white bg-opacity-70 px-2 py-1 rounded-full font-medium">
                  {node.children.length} report{node.children.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Children */}
        {hasChildren && node.isExpanded && (
          <div className="ml-8 border-l-2 border-gray-200 pl-4">
            {node.children.map((child, index) => (
              <div key={child.employee.uuid || child.employee.id || index} className="relative">
                {/* Tree branch indicator */}
                <div className="absolute -left-4 top-6 w-4 h-px bg-gray-300"></div>
                <div className="text-gray-400 text-sm mb-2 flex items-center">
                  <span className="mr-2">
                    {index === node.children.length - 1 ? '└──' : '├──'}
                  </span>
                </div>
                <div className="-mt-8">
                  {renderNode(child, index === node.children.length - 1)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const hierarchy = buildHierarchy();

  if (!hierarchy) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p>No organizational data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2">Organizational Hierarchy</h2>
        <p className="text-sm text-gray-600">
          Click on employee cards to view details • Click expand/collapse buttons to navigate the hierarchy
        </p>
      </div>
      
      <div className="overflow-auto">
        {renderNode(hierarchy)}
      </div>
    </div>
  );
};

export default HierarchicalOrgChart;
