import React from 'react';
import { TeamMember } from '../../types/team';
import EmployeeCard from './EmployeeCard';
import '../../styles/EmployeeManagement.css';

interface EmployeeGridProps {
  employees: TeamMember[];
  onEmployeeClick: (employee: TeamMember) => void;
}

const EmployeeGrid: React.FC<EmployeeGridProps> = ({ employees, onEmployeeClick }) => {
  if (employees.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No employees found</h3>
        <p className="text-gray-500">Try adjusting your search criteria or filters.</p>
      </div>
    );
  }

  return (
    <div className="employee-grid glass-morphism bg-gradient-to-br from-white via-slate-50 to-blue-50 p-4 rounded-2xl shadow-xl grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 animate-fadeIn">
      {employees.map((employee) => (
        <EmployeeCard
          key={employee.id || employee.name}
          employee={employee}
          onClick={() => onEmployeeClick(employee)}
        />
      ))}
    </div>
  );
};

export default EmployeeGrid;
