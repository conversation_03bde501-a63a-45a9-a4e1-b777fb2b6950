import React from 'react';
import { 
  X, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Award, 
  Users, 
  Building,
  Crown,
  MessageCircle,
  FileText,
  TrendingUp,
  Clock,
  User,
  Briefcase
} from 'lucide-react';
import { EmployeeProfile } from '../../types/teamDirectory';
import { DEPARTMENT_COLORS } from '../../types/teamDirectory';
import '../../styles/EmployeeManagement.css';

interface EmployeeProfileModalProps {
  employee: EmployeeProfile;
  onClose: () => void;
}

const EmployeeProfileModal: React.FC<EmployeeProfileModalProps> = ({ employee, onClose }) => {
  const departmentColor = DEPARTMENT_COLORS[employee.department as keyof typeof DEPARTMENT_COLORS] || '#6B7280';

  const handleEmailClick = () => {
    if (employee.email) {
      window.location.href = `mailto:${employee.email}`;
    }
  };

  const handlePhoneClick = () => {
    if (employee.phone) {
      window.location.href = `tel:${employee.phone}`;
    }
  };

  const InfoSection: React.FC<{
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
  }> = ({ title, icon, children }) => (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center space-x-2 mb-3">
        {icon}
        <h3 className="font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  );

  const InfoItem: React.FC<{
    label: string;
    value: string | number | undefined;
    icon?: React.ReactNode;
  }> = ({ label, value, icon }) => {
    if (!value) return null;
    
    return (
      <div className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-sm text-gray-600">{label}</span>
        </div>
        <span className="text-sm font-medium text-gray-900">{value}</span>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="relative">
          {/* Department Color Bar */}
          <div className="h-2 w-full gradient-text header-section animate-scaleIn" />
<div className="p-6 pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {employee.avatar ? (
                    <img 
                      src={employee.avatar} 
                      alt={employee.name}
                      className="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null}
                  <div 
                    className={`w-20 h-20 rounded-full flex items-center justify-center text-white font-bold text-xl border-4 border-white shadow-lg ${employee.avatar ? 'hidden' : ''}`}
                    style={{ backgroundColor: departmentColor }}
                  >
                    {employee.initials || employee.name.charAt(0)}
                  </div>
                  
                  {/* Leadership Badge */}
                  {employee.isLeadership && (
                    <div className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-2">
                      <Crown className="w-4 h-4 text-yellow-800" />
                    </div>
                  )}
                </div>
                
                <div>
                  <h2 className="text-2xl font-black gradient-text header-section animate-scaleIn">{employee.name}</h2>
                  <p className="text-lg text-gray-600">{employee.designation}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span 
                      className="inline-block w-3 h-3 rounded-full"
                      style={{ backgroundColor: departmentColor }}
                    />
                    <span className="text-gray-500">{employee.department}</span>
                    {employee.employeeCode && (
                      <>
                        <span className="text-gray-300">•</span>
                        <span className="text-gray-500">ID: {employee.employeeCode}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center space-x-3 mt-4">
              {employee.email && (
                <button
                  onClick={handleEmailClick}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Mail className="w-4 h-4" />
                  <span>Email</span>
                </button>
              )}
              
              {employee.phone && (
                <button
                  onClick={handlePhoneClick}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Phone className="w-4 h-4" />
                  <span>Call</span>
                </button>
              )}
              
              <button
                onClick={() => console.log('Message functionality to be implemented')}
                className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                <MessageCircle className="w-4 h-4" />
                <span>Message</span>
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 pt-0 max-h-[calc(90vh-200px)] overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Information */}
            <InfoSection
              title="Contact Information"
              icon={<Mail className="w-5 h-5 text-gray-600" />}
            >
              <div className="space-y-1">
                <InfoItem label="Email" value={employee.email} icon={<Mail className="w-4 h-4 text-gray-400" />} />
                <InfoItem label="Phone" value={employee.phone} icon={<Phone className="w-4 h-4 text-gray-400" />} />
                <InfoItem label="Personal Email" value={employee.personalInfo?.personalEmail} />
                <InfoItem label="Location" value={employee.location} icon={<MapPin className="w-4 h-4 text-gray-400" />} />
              </div>
            </InfoSection>

            {/* Job Information */}
            <InfoSection
              title="Job Information"
              icon={<Briefcase className="w-5 h-5 text-gray-600" />}
            >
              <div className="space-y-1">
                <InfoItem label="Department" value={employee.department} icon={<Building className="w-4 h-4 text-gray-400" />} />
                <InfoItem label="Team" value={employee.team} />
                <InfoItem label="Employment Type" value={employee.jobInfo?.employmentType} />
                <InfoItem label="Joining Date" value={employee.joiningDate ? new Date(employee.joiningDate).toLocaleDateString() : undefined} icon={<Calendar className="w-4 h-4 text-gray-400" />} />
                <InfoItem label="Probation End" value={employee.jobInfo?.probationEndDate ? new Date(employee.jobInfo.probationEndDate).toLocaleDateString() : undefined} />
              </div>
            </InfoSection>

            {/* Personal Information */}
            <InfoSection
              title="Personal Information"
              icon={<User className="w-5 h-5 text-gray-600" />}
            >
              <div className="space-y-1">
                <InfoItem label="Date of Birth" value={employee.personalInfo?.dateOfBirth ? new Date(employee.personalInfo.dateOfBirth).toLocaleDateString() : undefined} />
                <InfoItem label="Gender" value={employee.personalInfo?.gender} />
                <InfoItem label="Marital Status" value={employee.personalInfo?.maritalStatus} />
                <InfoItem label="Nationality" value={employee.personalInfo?.nationality} />
              </div>
            </InfoSection>

            {/* Leave Information */}
            <InfoSection
              title="Leave Balance"
              icon={<Clock className="w-5 h-5 text-gray-600" />}
            >
              <div className="space-y-1">
                <InfoItem label="Casual Leave" value={`${employee.leaveInfo?.casualLeaveBalance || 0} days`} />
                <InfoItem label="Sick Leave" value={`${employee.leaveInfo?.sickLeaveBalance || 0} days`} />
                <InfoItem label="Annual Leave" value={`${employee.leaveInfo?.annualLeaveBalance || 0} days`} />
                <InfoItem label="Total Leave Taken" value={`${employee.leaveInfo?.totalLeaveTaken || 0} days`} />
              </div>
            </InfoSection>
          </div>

          {/* Badges */}
          {employee.badges && employee.badges.length > 0 && (
            <div className="mt-6">
              <InfoSection
                title="Badges & Certifications"
                icon={<Award className="w-5 h-5 text-gray-600" />}
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {employee.badges.map((badge) => (
                    <div
                      key={badge.id}
                      className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200"
                    >
                      {badge.image && (
                        <img 
                          src={badge.image} 
                          alt={badge.name}
                          className="w-8 h-8 object-contain"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{badge.name}</p>
                        <p className="text-xs text-gray-500 truncate">{badge.description}</p>
                        {badge.awarded_date && (
                          <p className="text-xs text-gray-400">
                            Awarded: {new Date(badge.awarded_date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </InfoSection>
            </div>
          )}

          {/* Direct Reports */}
          {employee.directReports && employee.directReports.length > 0 && (
            <div className="mt-6">
              <InfoSection
                title={`Direct Reports (${employee.directReports.length})`}
                icon={<Users className="w-5 h-5 text-gray-600" />}
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {employee.directReports.map((report) => (
                    <div
                      key={report.id}
                      className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-gray-200"
                    >
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white font-medium text-sm"
                        style={{ backgroundColor: departmentColor }}
                      >
                        {report.name.split(' ').map(n => n.charAt(0)).join('').slice(0, 2)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{report.name}</p>
                        <p className="text-xs text-gray-500 truncate">{report.designation}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </InfoSection>
            </div>
          )}

          {/* Emergency Contact */}
          {employee.emergencyContact && (
            <div className="mt-6">
              <InfoSection
                title="Emergency Contact"
                icon={<Phone className="w-5 h-5 text-gray-600" />}
              >
                <div className="space-y-1">
                  <InfoItem label="Name" value={employee.emergencyContact.name} />
                  <InfoItem label="Phone" value={employee.emergencyContact.phone} />
                  <InfoItem label="Relationship" value={employee.emergencyContact.relationship} />
                </div>
              </InfoSection>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeProfileModal;
