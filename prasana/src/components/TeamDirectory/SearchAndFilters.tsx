import React, { useState, useMemo } from 'react';
import { Search, X, Calendar, MapPin, Award, Building, Users, Filter } from 'lucide-react';
import { SearchFilters } from '../../types/teamDirectory';
import { TeamMember } from '../../types/team';

interface SearchAndFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onClearFilters: () => void;
  employees: TeamMember[];
}

const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  employees
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Extract unique values for filter options
  const filterOptions = useMemo(() => {
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    const designations = [...new Set(employees.map(emp => emp.designation).filter(Boolean))];
    const locations = [...new Set(employees.map(emp => emp.location).filter(Boolean))];
    
    return {
      departments: departments.sort(),
      designations: designations.sort(),
      locations: locations.sort()
    };
  }, [employees]);

  const handleSearchChange = (value: string) => {
    onFiltersChange({
      ...filters,
      searchQuery: value
    });
  };

  const handleDepartmentToggle = (department: string) => {
    const newDepartments = filters.departments.includes(department)
      ? filters.departments.filter(d => d !== department)
      : [...filters.departments, department];
    
    onFiltersChange({
      ...filters,
      departments: newDepartments
    });
  };

  const handleDesignationToggle = (designation: string) => {
    const newDesignations = filters.designations.includes(designation)
      ? filters.designations.filter(d => d !== designation)
      : [...filters.designations, designation];
    
    onFiltersChange({
      ...filters,
      designations: newDesignations
    });
  };

  const handleLocationToggle = (location: string) => {
    const newLocations = filters.locations.includes(location)
      ? filters.locations.filter(l => l !== location)
      : [...filters.locations, location];
    
    onFiltersChange({
      ...filters,
      locations: newLocations
    });
  };

  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    onFiltersChange({
      ...filters,
      hireDateRange: {
        ...filters.hireDateRange,
        [field]: value
      }
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.searchQuery) count++;
    count += filters.departments.length;
    count += filters.designations.length;
    count += filters.locations.length;
    count += filters.skills.length;
    if (filters.hireDateRange.start || filters.hireDateRange.end) count++;
    return count;
  };

  const departmentColors: Record<string, string> = {
    'NEXUS': 'bg-blue-100 text-blue-800 border-blue-200',
    'DYNAMIX': 'bg-green-100 text-green-800 border-green-200',
    'TITAN': 'bg-orange-100 text-orange-800 border-orange-200',
    'ATHENA': 'bg-purple-100 text-purple-800 border-purple-200'
  };

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        {/* Search Bar */}
        <div className="flex items-center space-x-4 mb-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search employees by name, email, designation, or employee code..."
              value={filters.searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            />
            {filters.searchQuery && (
              <button
                onClick={() => handleSearchChange('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
          
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
              showAdvanced
                ? 'bg-blue-50 border-blue-200 text-blue-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Filter className="w-4 h-4" />
            <span>Advanced</span>
          </button>

          {getActiveFilterCount() > 0 && (
            <button
              onClick={onClearFilters}
              className="flex items-center space-x-2 px-4 py-2 bg-red-50 border border-red-200 text-red-700 rounded-lg hover:bg-red-100 transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Clear ({getActiveFilterCount()})</span>
            </button>
          )}
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-4 pt-4 border-t border-gray-200">
            {/* Departments */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <Building className="w-4 h-4" />
                <span>Departments</span>
              </label>
              <div className="flex flex-wrap gap-2">
                {filterOptions.departments.map((department) => (
                  <button
                    key={department}
                    onClick={() => handleDepartmentToggle(department)}
                    className={`px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                      filters.departments.includes(department)
                        ? departmentColors[department] || 'bg-gray-100 text-gray-800 border-gray-200'
                        : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {department}
                  </button>
                ))}
              </div>
            </div>

            {/* Designations */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <Users className="w-4 h-4" />
                <span>Designations</span>
              </label>
              <div className="flex flex-wrap gap-2">
                {filterOptions.designations.map((designation) => (
                  <button
                    key={designation}
                    onClick={() => handleDesignationToggle(designation)}
                    className={`px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                      filters.designations.includes(designation)
                        ? 'bg-blue-100 text-blue-800 border-blue-200'
                        : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {designation}
                  </button>
                ))}
              </div>
            </div>

            {/* Locations */}
            {filterOptions.locations.length > 0 && (
              <div>
                <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                  <MapPin className="w-4 h-4" />
                  <span>Locations</span>
                </label>
                <div className="flex flex-wrap gap-2">
                  {filterOptions.locations.map((location) => (
                    <button
                      key={location}
                      onClick={() => handleLocationToggle(location)}
                      className={`px-3 py-1 rounded-full text-sm font-medium border transition-colors ${
                        filters.locations.includes(location)
                          ? 'bg-green-100 text-green-800 border-green-200'
                          : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {location}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Hire Date Range */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4" />
                <span>Hire Date Range</span>
              </label>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">From:</label>
                  <input
                    type="date"
                    value={filters.hireDateRange.start || ''}
                    onChange={(e) => handleDateRangeChange('start', e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-600">To:</label>
                  <input
                    type="date"
                    value={filters.hireDateRange.end || ''}
                    onChange={(e) => handleDateRangeChange('end', e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {getActiveFilterCount() > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>Active filters:</span>
              <div className="flex flex-wrap gap-1">
                {filters.searchQuery && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                    Search: "{filters.searchQuery}"
                  </span>
                )}
                {filters.departments.map(dept => (
                  <span key={dept} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                    {dept}
                  </span>
                ))}
                {filters.designations.map(designation => (
                  <span key={designation} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                    {designation}
                  </span>
                ))}
                {filters.locations.map(location => (
                  <span key={location} className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                    {location}
                  </span>
                ))}
                {(filters.hireDateRange.start || filters.hireDateRange.end) && (
                  <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">
                    Date: {filters.hireDateRange.start || '...'} - {filters.hireDateRange.end || '...'}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchAndFilters;
