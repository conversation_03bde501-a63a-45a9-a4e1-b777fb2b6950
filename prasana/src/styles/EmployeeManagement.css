/* Professional Employee Management Dashboard Styles */

/* Clean, professional animations */
@keyframes fadeIn {
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7), 0 0 0 0 rgba(251, 191, 36, 0.5);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(245, 158, 11, 0.2), 0 0 0 16px rgba(251, 191, 36, 0.1);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 1.5s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Subtle hover effects */
@keyframes lift {
  to {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Professional component styles */
.employee-card {
  animation: fadeIn 0.3s ease-out;
  transition: all 0.2s ease-in-out;
}

.employee-card:hover {
  animation: lift 0.2s ease-out forwards;
}

.filter-section {
  animation: slideIn 0.3s ease-out;
}

.header-section {
  animation: scaleIn 0.3s ease-out;
}

.chart-bar {
  animation: fadeIn 0.4s ease-out;
  transition: all 0.2s ease-in-out;
}

.chart-bar:hover {
  transform: scale(1.05);
}

/* Professional loading states */
.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Clean focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Professional responsive design */
@media (max-width: 640px) {
  .employee-card {
    margin-bottom: 1rem;
  }

  .chart-container {
    overflow-x: auto;
    padding-bottom: 1rem;
  }

  .chart-bar {
    min-width: 48px;
  }

  .header-stats {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
  }

  .filter-section {
    padding: 1rem;
  }

  .analytics-section {
    padding: 1rem;
  }

  .employee-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) {
  .employee-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .employee-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .employee-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Clean scrollbar styling */
.chart-container::-webkit-scrollbar {
  height: 4px;
}

.chart-container::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

.chart-container::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Enhanced focus states for accessibility */
.filter-input:focus,
.filter-select:focus {
  outline: none;
  ring: 4px;
  ring-color: rgba(59, 130, 246, 0.3);
  border-color: #3b82f6;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card hover glow effect */
.card-glow:hover {
  box-shadow: 
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    0 0 20px rgba(59, 130, 246, 0.1);
}

/* Performance indicators */
.performance-excellent {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.4);
}

.performance-good {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.4);
}

.performance-average {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.4);
}

.performance-low {
  background: linear-gradient(135deg, #94a3b8, #64748b);
  box-shadow: 0 4px 14px 0 rgba(148, 163, 184, 0.4);
}

/* Micro-interactions */
.interactive-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
}

/* Status indicators */
.status-online {
  position: relative;
}

.status-online::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 0.5rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dark-mode-card {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(71, 85, 105, 0.3);
  }
  
  .dark-mode-text {
    color: #e2e8f0;
  }
  
  .dark-mode-text-muted {
    color: #94a3b8;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .employee-card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .chart-container {
    height: auto !important;
  }
}
