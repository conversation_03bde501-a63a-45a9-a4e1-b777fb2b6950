import { supabase } from '../supabaseClient';

export interface AuthUser {
  id: string;
  email: string;
  created_at: string;
  email_confirmed_at: string | null;
}

export interface Employee {
  id: string;
  user_id: string | null;
  first_name: string;
  last_name: string;
  email: string;
  designation: string;
  department: string;
  status: string;
}

export interface SyncResult {
  success: boolean;
  message: string;
  details?: any;
}

/**
 * Get all auth users from the system
 */
export const getAuthUsers = async (): Promise<AuthUser[]> => {
  try {
    const { data, error } = await supabase
      .from('auth.users')
      .select('id, email, created_at, email_confirmed_at');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching auth users:', error);
    return [];
  }
};

/**
 * Get all active employees (excluding system administrators)
 */
export const getActiveEmployees = async (): Promise<Employee[]> => {
  try {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('status', 'active')
      .neq('designation', 'System Administrator');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching employees:', error);
    return [];
  }
};

/**
 * Sync employee email with auth system email
 */
export const syncEmployeeEmail = async (employeeId: string, authEmail: string): Promise<SyncResult> => {
  try {
    const { error } = await supabase
      .from('employees')
      .update({ email: authEmail })
      .eq('id', employeeId);

    if (error) throw error;

    return {
      success: true,
      message: `Employee email updated to ${authEmail}`
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to sync email: ${error.message}`,
      details: error
    };
  }
};

/**
 * Create employee record for existing auth user
 */
export const createEmployeeForAuthUser = async (authUser: AuthUser): Promise<SyncResult> => {
  try {
    // Extract name from email
    const emailParts = authUser.email.split('@')[0];
    const nameParts = emailParts.split('.');
    
    const firstName = nameParts[0] ? 
      nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1) : 'User';
    const lastName = nameParts[1] ? 
      nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1) : 'Name';

    const { error } = await supabase
      .from('employees')
      .insert({
        user_id: authUser.id,
        employee_id: `EMP_${Date.now()}`,
        first_name: firstName,
        last_name: lastName,
        email: authUser.email,
        designation: 'Associate Trainee',
        department: 'General',
        status: 'active',
        joining_date: new Date().toISOString().split('T')[0]
      });

    if (error) throw error;

    return {
      success: true,
      message: `Employee record created for ${authUser.email}`
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to create employee: ${error.message}`,
      details: error
    };
  }
};

/**
 * Create auth user for existing employee
 */
export const createAuthUserForEmployee = async (employee: Employee, tempPassword: string = 'TempPassword123!'): Promise<SyncResult> => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email: employee.email,
      password: tempPassword,
      options: {
        data: {
          full_name: `${employee.first_name} ${employee.last_name}`.trim()
        }
      }
    });

    if (error) throw error;

    if (data.user) {
      // Update employee with user_id
      const { error: updateError } = await supabase
        .from('employees')
        .update({ user_id: data.user.id })
        .eq('id', employee.id);

      if (updateError) throw updateError;

      return {
        success: true,
        message: `Auth user created for ${employee.first_name} ${employee.last_name}`,
        details: { tempPassword, userId: data.user.id }
      };
    } else {
      throw new Error('No user data returned from signup');
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to create auth user: ${error.message}`,
      details: error
    };
  }
};

/**
 * Clear invalid user_id reference from employee
 */
export const clearInvalidUserReference = async (employeeId: string): Promise<SyncResult> => {
  try {
    const { error } = await supabase
      .from('employees')
      .update({ user_id: null })
      .eq('id', employeeId);

    if (error) throw error;

    return {
      success: true,
      message: 'Invalid user reference cleared'
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to clear reference: ${error.message}`,
      details: error
    };
  }
};

/**
 * Send password reset email to user
 */
export const sendPasswordReset = async (email: string): Promise<SyncResult> => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    });

    if (error) throw error;

    return {
      success: true,
      message: `Password reset email sent to ${email}`
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to send reset email: ${error.message}`,
      details: error
    };
  }
};

/**
 * Comprehensive sync analysis
 */
export const analyzeSync = async () => {
  const authUsers = await getAuthUsers();
  const employees = await getActiveEmployees();

  const issues = {
    emailMismatches: [] as Array<{ employee: Employee; authUser: AuthUser }>,
    missingEmployees: [] as AuthUser[],
    missingAuthUsers: [] as Employee[],
    orphanedEmployees: [] as Employee[],
    invalidReferences: [] as Employee[]
  };

  // Check for email mismatches
  employees.forEach(employee => {
    if (employee.user_id) {
      const authUser = authUsers.find(auth => auth.id === employee.user_id);
      if (authUser && authUser.email !== employee.email) {
        issues.emailMismatches.push({ employee, authUser });
      } else if (!authUser) {
        issues.invalidReferences.push(employee);
      }
    } else {
      issues.orphanedEmployees.push(employee);
    }
  });

  // Check for auth users without employees
  authUsers.forEach(authUser => {
    const employee = employees.find(emp => emp.user_id === authUser.id);
    if (!employee) {
      issues.missingEmployees.push(authUser);
    }
  });

  return {
    authUsers,
    employees,
    issues,
    totalIssues: Object.values(issues).reduce((sum, arr) => sum + arr.length, 0)
  };
};

/**
 * Auto-fix common sync issues
 */
export const autoFixSyncIssues = async () => {
  const analysis = await analyzeSync();
  const results: SyncResult[] = [];

  // Fix email mismatches
  for (const { employee, authUser } of analysis.issues.emailMismatches) {
    const result = await syncEmployeeEmail(employee.id, authUser.email);
    results.push(result);
  }

  // Create employees for auth users
  for (const authUser of analysis.issues.missingEmployees) {
    const result = await createEmployeeForAuthUser(authUser);
    results.push(result);
  }

  // Clear invalid references
  for (const employee of analysis.issues.invalidReferences) {
    const result = await clearInvalidUserReference(employee.id);
    results.push(result);
  }

  const successCount = results.filter(r => r.success).length;
  const failureCount = results.filter(r => !r.success).length;

  return {
    success: failureCount === 0,
    message: `Auto-fix completed: ${successCount} fixed, ${failureCount} failed`,
    details: { results, successCount, failureCount }
  };
};

/**
 * Get sync status summary
 */
export const getSyncStatus = async () => {
  const analysis = await analyzeSync();
  
  return {
    isInSync: analysis.totalIssues === 0,
    authUserCount: analysis.authUsers.length,
    employeeCount: analysis.employees.length,
    issueCount: analysis.totalIssues,
    issues: analysis.issues
  };
};
