# 🏢 Dayforce-like HRMS System

A comprehensive Human Resource Management System built with React, TypeScript, and Supabase, inspired by Dayforce.

## 🎯 Overview

This HRMS system provides a complete solution for managing human resources with features for employees, managers, and HR administrators. It includes employee self-service capabilities, attendance tracking, leave management, and more.

## ✨ Features

### 🏠 Dashboard
- **Personalized Widgets**: Leave status, attendance summary, upcoming events
- **Quick Actions**: Apply leave, clock in/out, view payslip, update profile
- **Real-time Notifications**: Leave approvals, birthdays, announcements
- **Manager View**: Team overview, pending approvals, team statistics

### 👤 Employee Profile Management
- **Personal Information**: Name, contact details, emergency contacts
- **Job Information**: Designation, department, manager, employment type
- **Document Management**: Upload and manage ID proofs, certificates
- **Address Management**: Residential and mailing addresses
- **Profile Picture**: Upload and manage profile photos

### ⏰ Time & Attendance
- **Clock In/Out**: Web-based time tracking with location
- **Calendar View**: Monthly attendance calendar with status indicators
- **Attendance Summary**: Monthly/yearly statistics and reports
- **Manual Timesheet**: Add and edit time entries manually
- **Weekly View**: Visual weekly timesheet with project tracking
- **Integration**: Seamlessly works with existing timesheet system
- **Overtime Tracking**: Automatic calculation of overtime hours

### 🏖️ Leave Management
- **Leave Application**: Apply for different types of leaves (CL, SL, AL, etc.)
- **Leave Balance**: Real-time tracking of available leave days
- **Approval Workflow**: Manager approval with comments
- **Leave Calendar**: Visual representation of team leaves
- **Holiday Calendar**: Company-wide holiday management

### 💰 Payroll & Compensation (Coming Soon)
- **Payslip Generation**: Downloadable PDF payslips
- **Salary Components**: Basic, allowances, deductions breakdown
- **Tax Information**: Tax deductions and declarations
- **Bonus Management**: Performance and festival bonuses

### 📊 Performance Management (Coming Soon)
- **Performance Reviews**: Self and manager assessments
- **Goal Setting**: Career objectives and KPIs
- **360-Degree Feedback**: Multi-source feedback system
- **Training Records**: Certifications and skill development

### 📋 Self-Service Requests (Coming Soon)
- **Address Changes**: Request address updates
- **Bank Details**: Update banking information
- **Emergency Contacts**: Modify emergency contact details
- **Request Tracking**: Monitor request status and approvals

## 🛠️ Technical Architecture

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Context API** for state management
- **Custom Hooks** for business logic

### Backend
- **Supabase** for database and authentication
- **PostgreSQL** with Row Level Security (RLS)
- **Real-time subscriptions** for live updates
- **File storage** for document management

### Database Schema
```sql
-- Core Tables
- employees (employee information)
- attendance (time tracking)
- leave_types (leave categories)
- leave_applications (leave requests)
- employee_leave_balance (leave balances)
- holidays (company holidays)
- notifications (system notifications)
- employee_documents (file uploads)
- payroll (salary information)
- performance_reviews (appraisals)
- self_service_requests (change requests)
```

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- Supabase account
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd prasana
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create a new Supabase project
   - Run the migration files in order:
     ```bash
     # In Supabase SQL Editor
     -- Run 20240321_hrms_schema.sql
     -- Run 20240322_hrms_sample_data.sql
     -- Run 20240323_hrms_auth_integration.sql
     ```

4. **Configure environment variables**
   ```bash
   # Create .env file
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Access the HRMS**
   - Navigate to the application
   - Sign up or log in with your credentials
   - Click on "HRMS" in the sidebar
   - Your employee profile will be automatically created

## 📱 User Roles & Permissions

### Employee
- View personal dashboard
- Manage profile and documents
- Clock in/out and view attendance
- Apply for leaves and view balance
- View payslips and tax information

### Manager
- All employee permissions
- View team attendance and leaves
- Approve/reject leave requests
- Access team performance data
- Generate team reports

### HR Administrator
- All manager permissions
- Manage all employees
- Configure leave types and policies
- Process payroll
- Generate company-wide reports
- Manage holidays and announcements

## 🔧 Configuration

### Leave Types
Configure different leave types in the `leave_types` table:
```sql
INSERT INTO leave_types (name, code, max_days_per_year, carry_forward_allowed)
VALUES ('Casual Leave', 'CL', 12, true);
```

### Holidays
Add company holidays:
```sql
INSERT INTO holidays (name, date, type, description)
VALUES ('New Year', '2024-01-01', 'public', 'New Year celebration');
```

### Notifications
The system automatically generates notifications for:
- Leave approvals/rejections
- Birthday reminders
- Payslip availability
- Policy updates

## 🔐 Security Features

### Row Level Security (RLS)
- Employees can only access their own data
- Managers can view their team's data
- HR can access all employee data
- Secure file uploads with access controls

### Authentication
- Supabase Auth integration
- JWT token-based authentication
- Role-based access control
- Session management

## 📊 Analytics & Reporting

### Dashboard Metrics
- Attendance percentage
- Leave utilization
- Team productivity
- Pending approvals

### Reports (Coming Soon)
- Monthly attendance reports
- Leave analysis
- Payroll summaries
- Performance analytics

## 🔄 API Integration

### HRMS Service Layer
The system uses a service layer pattern for API calls:

```typescript
// Example usage
import { hrmsService } from '../services/hrmsService';

// Get current employee
const employee = await hrmsService.getCurrentEmployee();

// Apply for leave
await hrmsService.applyLeave({
  leave_type_id: 'uuid',
  start_date: '2024-03-15',
  end_date: '2024-03-17',
  reason: 'Family vacation'
});
```

### Context API
Use React Context for state management:

```typescript
import { useHRMS, useLeaveManagement } from '../contexts/HRMSContext';

// In your component
const { currentEmployee, notifications } = useHRMS();
const { applyLeave, leaveBalance } = useLeaveManagement();
```

## 🚧 Roadmap

### Phase 1 (Completed)
- ✅ Employee profile management
- ✅ Leave management
- ✅ Manual timesheet entry
- ✅ User-specific HRMS access
- ✅ Authentication integration
- ✅ Basic dashboard
- ✅ Document upload

### Phase 2 (In Progress)
- 🔄 Payroll management
- 🔄 Performance reviews
- 🔄 Advanced reporting
- 🔄 Mobile responsiveness

### Phase 3 (Planned)
- 📋 Training management
- 📋 Recruitment module
- 📋 Asset management
- 📋 Expense management
- 📋 Mobile app (React Native)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Contact the development team

---

**Built with ❤️ using React, TypeScript, and Supabase**
