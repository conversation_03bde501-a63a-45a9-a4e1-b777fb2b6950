// Debug script for IT Support access issues
// Run this in the browser console when logged <NAME_EMAIL>

async function debugITSupportAccess() {
  console.log('🔧 Starting IT Support access debug...');
  
  try {
    // Check if supabase is available
    const supabase = window.supabase || (await import('/src/supabaseClient.js')).supabase;
    
    if (!supabase) {
      console.error('❌ Supabase client not found');
      return;
    }

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ No authenticated user found:', userError);
      return;
    }

    console.log('👤 Current user info:', {
      id: user.id,
      email: user.email,
      metadata: user.user_metadata
    });

    // Check email lists
    const ADMIN_EMAILS = [
      '<EMAIL>',        // Sri Ram (ATHENA SDM)
      '<EMAIL>',       // <PERSON><PERSON><PERSON> (DYNAMIX SDM) 
      '<EMAIL>',       // <PERSON><PERSON><PERSON> (TITAN SDM)
      '<EMAIL>',               // <PERSON><PERSON><PERSON> (NEXUS SDM) - Exception as requested
      '<EMAIL>',  // Eashwara Prasadh alternate email
      '<EMAIL>'    // IT Support - Super Admin
    ];

    const SUPER_ADMIN_EMAILS = [
      '<EMAIL>',                    // Eashwara Prasadh
      '<EMAIL>',       // Eashwara Prasadh alternate
      '<EMAIL>'         // IT Support - UID: 34978690-150f-4a34-a4cb-5e2602f5dbee
    ];

    const isAdmin = ADMIN_EMAILS.includes(user.email);
    const isSuperAdmin = SUPER_ADMIN_EMAILS.includes(user.email);

    console.log('🔍 Access check results:', {
      email: user.email,
      isAdmin,
      isSuperAdmin,
      adminEmails: ADMIN_EMAILS,
      superAdminEmails: SUPER_ADMIN_EMAILS
    });

    // Check if HR Dashboard should be accessible
    if (isSuperAdmin) {
      console.log('✅ User should have access to HR Dashboard');
    } else {
      console.log('❌ User should NOT have access to HR Dashboard');
    }

    // Check if sidebar shows HR Dashboard
    const hrDashboardButton = document.querySelector('button[onclick*="hr-dashboard"]');
    if (hrDashboardButton) {
      console.log('✅ HR Dashboard button found in sidebar');
    } else {
      console.log('❌ HR Dashboard button NOT found in sidebar');
    }

    // Check React context (if available)
    if (window.React && window.React.version) {
      console.log('⚛️ React version:', window.React.version);
    }

    // Try to access the user context from React DevTools
    try {
      const reactFiber = document.querySelector('#root')._reactInternalInstance || 
                        document.querySelector('#root')._reactInternals;
      if (reactFiber) {
        console.log('⚛️ React fiber found, checking context...');
      }
    } catch (e) {
      console.log('⚛️ Could not access React internals');
    }

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email
      },
      access: {
        isAdmin,
        isSuperAdmin
      },
      expected: {
        shouldHaveHRAccess: isSuperAdmin,
        shouldSeeHRButton: isSuperAdmin
      }
    };

  } catch (error) {
    console.error('❌ Error debugging IT Support access:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Function to force refresh user context
async function forceRefreshUserContext() {
  console.log('🔄 Forcing user context refresh...');
  
  try {
    const supabase = window.supabase || (await import('/src/supabaseClient.js')).supabase;
    
    // Sign out and back in to refresh context
    const currentSession = await supabase.auth.getSession();
    if (currentSession.data.session) {
      console.log('🔄 Refreshing session...');
      await supabase.auth.refreshSession();
      console.log('✅ Session refreshed');
    }
    
    // Trigger a page reload to reset React context
    setTimeout(() => {
      console.log('🔄 Reloading page to reset context...');
      window.location.reload();
    }, 1000);
    
  } catch (error) {
    console.error('❌ Error refreshing context:', error);
  }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🚀 IT Support Debug Script Loaded');
  console.log('Run debugITSupportAccess() to check access');
  console.log('Run forceRefreshUserContext() to refresh and reload');
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { debugITSupportAccess, forceRefreshUserContext };
}
