// Script to fix database permissions and create missing employee records
// This addresses the 406 Not Acceptable and 403 Forbidden errors

async function fixDatabasePermissions() {
  console.log('🔧 Starting database permissions fix...');
  
  try {
    // This assumes supabase client is available globally
    const supabase = window.supabase || (await import('/src/supabaseClient.js')).supabase;
    
    if (!supabase) {
      throw new Error('Supabase client not found');
    }

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      throw new Error('No authenticated user found');
    }

    console.log('👤 Current user:', user.email, 'UID:', user.id);

    // Step 1: Check if employee record exists
    console.log('🔍 Checking for existing employee record...');
    
    const { data: existingEmployee, error: checkError } = await supabase
      .from('employees')
      .select('*')
      .eq('user_id', user.id)
      .maybeSingle();

    if (checkError) {
      console.log('⚠️ Error checking employee:', checkError);
      console.log('This might be due to RLS policies. Attempting to create record...');
    }

    if (existingEmployee) {
      console.log('✅ Employee record already exists:', existingEmployee);
      return {
        success: true,
        message: 'Employee record already exists',
        employee: existingEmployee
      };
    }

    // Step 2: Create employee record for IT Support user
    console.log('📝 Creating employee record...');
    
    const employeeData = {
      user_id: user.id,
      employee_id: `EMP_${Date.now()}`,
      first_name: user.email === '<EMAIL>' ? 'IT Support' : user.email?.split('@')[0] || 'User',
      last_name: '',
      email: user.email,
      designation: user.email === '<EMAIL>' ? 'IT Support Specialist' : 'Employee',
      department: user.email === '<EMAIL>' ? 'IT' : 'GENERAL',
      team: user.email === '<EMAIL>' ? 'IT' : 'GENERAL',
      status: 'active',
      joining_date: new Date().toISOString().split('T')[0],
      hire_date: new Date().toISOString().split('T')[0],
      employee_code: user.email === '<EMAIL>' ? 'IT001' : `EMP${Date.now().toString().slice(-4)}`,
      casual_leave_balance: 12,
      sick_leave_balance: 12,
      annual_leave_balance: 21,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('📋 Employee data to create:', employeeData);

    const { data: newEmployee, error: createError } = await supabase
      .from('employees')
      .insert([employeeData])
      .select()
      .single();

    if (createError) {
      console.log('❌ Error creating employee:', createError);
      
      // Try alternative approach - direct insert without RLS
      console.log('🔄 Trying alternative approach...');
      
      const { data: altEmployee, error: altError } = await supabase
        .rpc('create_employee_record', {
          p_user_id: user.id,
          p_email: user.email,
          p_first_name: employeeData.first_name,
          p_designation: employeeData.designation
        });

      if (altError) {
        throw new Error(`Failed to create employee record: ${altError.message}`);
      }

      console.log('✅ Employee created via RPC:', altEmployee);
      return {
        success: true,
        message: 'Employee record created successfully via RPC',
        employee: altEmployee
      };
    }

    console.log('✅ Employee record created successfully:', newEmployee);

    // Step 3: Verify the creation
    const { data: verifyEmployee, error: verifyError } = await supabase
      .from('employees')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (verifyError) {
      console.log('⚠️ Could not verify employee creation:', verifyError);
    } else {
      console.log('✅ Verification successful:', verifyEmployee);
    }

    return {
      success: true,
      message: 'Employee record created and verified successfully',
      employee: newEmployee
    };

  } catch (error) {
    console.error('❌ Error fixing database permissions:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Function to create the RPC function in Supabase (run this in Supabase SQL editor)
function getSQLForRPCFunction() {
  return `
-- Create RPC function to handle employee creation with proper permissions
CREATE OR REPLACE FUNCTION create_employee_record(
  p_user_id UUID,
  p_email TEXT,
  p_first_name TEXT,
  p_designation TEXT DEFAULT 'Employee'
)
RETURNS TABLE(id BIGINT, user_id UUID, email TEXT, first_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Insert employee record
  RETURN QUERY
  INSERT INTO employees (
    user_id,
    employee_id,
    first_name,
    last_name,
    email,
    designation,
    department,
    team,
    status,
    joining_date,
    hire_date,
    employee_code,
    casual_leave_balance,
    sick_leave_balance,
    annual_leave_balance,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    'EMP_' || EXTRACT(EPOCH FROM NOW())::BIGINT,
    p_first_name,
    '',
    p_email,
    p_designation,
    CASE WHEN p_email = '<EMAIL>' THEN 'IT' ELSE 'GENERAL' END,
    CASE WHEN p_email = '<EMAIL>' THEN 'IT' ELSE 'GENERAL' END,
    'active',
    CURRENT_DATE,
    CURRENT_DATE,
    CASE WHEN p_email = '<EMAIL>' THEN 'IT001' ELSE 'EMP' || (EXTRACT(EPOCH FROM NOW())::BIGINT % 10000) END,
    12,
    12,
    21,
    NOW(),
    NOW()
  )
  RETURNING employees.id, employees.user_id, employees.email, employees.first_name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_employee_record TO authenticated;
`;
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🚀 Database Permissions Fix Script Loaded');
  console.log('Run fixDatabasePermissions() to execute the fix');
  console.log('Run getSQLForRPCFunction() to get SQL for Supabase');
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { fixDatabasePermissions, getSQLForRPCFunction };
}
