-- =====================================================
-- TEAM MEMBERS SETUP SCRIPT
-- This script creates employee records and assigns roles/badges
-- Note: User accounts must be created through Supabase Auth first
-- =====================================================

-- =====================================================
-- HELPER FUNCTION TO CREATE EMPLOYEE WITH ROLE AND BADGES
-- =====================================================

CREATE OR REPLACE FUNCTION create_employee_with_role_and_badges(
    p_user_id UUID,
    p_employee_id VARCHAR,
    p_first_name VARCHAR,
    p_last_name VARCHAR,
    p_email VARCHAR,
    p_designation VARCHAR,
    p_team_name VARCHAR,
    p_role_name VARCHAR,
    p_badge_names TEXT[]
) RETURNS UUID AS $$
DECLARE
    v_employee_id UUID;
    v_team_id UUID;
    v_role_id UUID;
    v_badge_id UUID;
    v_badge_name TEXT;
BEGIN
    -- Get team ID
    SELECT id INTO v_team_id FROM teams WHERE name = p_team_name;
    
    -- Insert employee
    INSERT INTO employees (
        user_id, employee_id, first_name, last_name, email,
        designation, department, team_id, joining_date, status
    ) VALUES (
        p_user_id, p_employee_id, p_first_name, p_last_name, p_email,
        p_designation, p_team_name, v_team_id, CURRENT_DATE, 'active'
    ) RETURNING id INTO v_employee_id;
    
    -- Assign system role
    SELECT id INTO v_role_id FROM system_roles WHERE name = p_role_name;
    INSERT INTO user_role_assignments (user_id, system_role_id, assigned_by)
    VALUES (p_user_id, v_role_id, p_user_id);
    
    -- Assign badges
    FOREACH v_badge_name IN ARRAY p_badge_names
    LOOP
        SELECT id INTO v_badge_id FROM badges WHERE name = v_badge_name;
        IF v_badge_id IS NOT NULL THEN
            INSERT INTO employee_badges (employee_id, badge_id, assigned_by, assigned_date)
            VALUES (v_employee_id, v_badge_id, v_employee_id, CURRENT_DATE);
        END IF;
    END LOOP;
    
    -- Initialize leave balances
    INSERT INTO employee_leave_balances (employee_id, leave_type_id, year, allocated_days)
    SELECT v_employee_id, lt.id, EXTRACT(YEAR FROM CURRENT_DATE), lt.max_days_per_year
    FROM leave_types lt WHERE lt.is_active = true;
    
    RETURN v_employee_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TEAM MEMBER CREATION WITH GENERATED UUIDs
-- =====================================================

-- This script creates employee records with generated UUIDs for each team member.
-- Using @technosprint.net email domain for all team members.
-- Note: This bypasses Supabase Auth by inserting directly into auth.users first

-- =====================================================
-- ATHENA TEAM MEMBERS
-- =====================================================

-- Generate UUIDs for ATHENA team members
DO $$
DECLARE
    sri_ram_uuid UUID := uuid_generate_v4();
    selvendrane_uuid UUID := uuid_generate_v4();
    maheshwaran_uuid UUID := uuid_generate_v4();
    theepatharan_uuid UUID := uuid_generate_v4();
    fazeela_uuid UUID := uuid_generate_v4();
    sivaranjani_uuid UUID := uuid_generate_v4();
BEGIN
    -- Insert into auth.users first (required for foreign key constraint)
    INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, aud, role) VALUES
    (sri_ram_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (selvendrane_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (maheshwaran_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (theepatharan_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (fazeela_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (sivaranjani_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated');

    -- Insert users into public.users table
    INSERT INTO users (id, email, full_name) VALUES
    (sri_ram_uuid, '<EMAIL>', 'Sri Ram'),
    (selvendrane_uuid, '<EMAIL>', 'Selvendrane'),
    (maheshwaran_uuid, '<EMAIL>', 'Maheshwaran'),
    (theepatharan_uuid, '<EMAIL>', 'Theepatharan'),
    (fazeela_uuid, '<EMAIL>', 'Fazeela'),
    (sivaranjani_uuid, '<EMAIL>', 'Sivaranjani');

    -- Sri Ram (Service Delivery Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        sri_ram_uuid,
        'EMP001',
        'Sri',
        'Ram',
        '<EMAIL>',
        'Service Delivery Manager',
        'ATHENA',
        'Admin',
        ARRAY['Service Delivery Manager', 'Associate Trainee', 'Leadership']
    );

    -- Selvendrane (Technical Account Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        selvendrane_uuid,
        'EMP002',
        'Selvendrane',
        '',
        '<EMAIL>',
        'Technical Account Manager',
        'ATHENA',
        'Admin',
        ARRAY['Technical Account Manager', 'Associate Trainee', 'Leadership']
    );

    -- Maheshwaran (Client Experience Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        maheshwaran_uuid,
        'EMP003',
        'Maheshwaran',
        '',
        '<EMAIL>',
        'Client Experience Manager',
        'ATHENA',
        'Admin',
        ARRAY['Client Experience Manager', 'Associate Trainee', 'Leadership']
    );

    -- Theepatharan (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        theepatharan_uuid,
        'EMP004',
        'Theepatharan',
        '',
        '<EMAIL>',
        'Associate Trainee',
        'ATHENA',
        'User',
        ARRAY['Associate Trainee']
    );

    -- Fazeela (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        fazeela_uuid,
        'EMP005',
        'Fazeela',
        '',
        '<EMAIL>',
        'Associate Trainee',
        'ATHENA',
        'User',
        ARRAY['Associate Trainee']
    );

    -- Sivaranjani (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        sivaranjani_uuid,
        'EMP006',
        'Sivaranjani',
        '',
        '<EMAIL>',
        'Associate Trainee',
        'ATHENA',
        'User',
        ARRAY['Associate Trainee']
    );
END $$;

-- =====================================================
-- DYNAMIX TEAM MEMBERS
-- =====================================================

-- Generate UUIDs for DYNAMIX team members
DO $$
DECLARE
    yuvaraj_uuid UUID := uuid_generate_v4();
    purushoth_uuid UUID := uuid_generate_v4();
    kiyshore_uuid UUID := uuid_generate_v4();
    praveen_uuid UUID := uuid_generate_v4();
BEGIN
    -- Insert into auth.users first
    INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, aud, role) VALUES
    (yuvaraj_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (purushoth_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (kiyshore_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (praveen_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated');

    -- Insert users first
    INSERT INTO users (id, email, full_name) VALUES
    (yuvaraj_uuid, '<EMAIL>', 'Yuvaraj'),
    (purushoth_uuid, '<EMAIL>', 'Purushoth'),
    (kiyshore_uuid, '<EMAIL>', 'Kiyshore K'),
    (praveen_uuid, '<EMAIL>', 'Praveen Dommeti');

    -- Yuvaraj (Service Delivery Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        yuvaraj_uuid,
        'EMP007',
        'Yuvaraj',
        '',
        '<EMAIL>',
        'Service Delivery Manager',
        'DYNAMIX',
        'Admin',
        ARRAY['Service Delivery Manager', 'Associate Trainee', 'Leadership']
    );

    -- Purushoth (Technical Account Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        purushoth_uuid,
        'EMP008',
        'Purushoth',
        '',
        '<EMAIL>',
        'Technical Account Manager',
        'DYNAMIX',
        'Admin',
        ARRAY['Technical Account Manager', 'Associate Trainee', 'Leadership']
    );

    -- Kiyshore K (Client Experience Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        kiyshore_uuid,
        'EMP009',
        'Kiyshore',
        'K',
        '<EMAIL>',
        'Client Experience Manager',
        'DYNAMIX',
        'Admin',
        ARRAY['Client Experience Manager', 'Associate Trainee', 'Leadership']
    );

    -- Praveen Dommeti (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        praveen_uuid,
        'EMP010',
        'Praveen',
        'Dommeti',
        '<EMAIL>',
        'Associate Trainee',
        'DYNAMIX',
        'User',
        ARRAY['Associate Trainee']
    );
END $$;

-- =====================================================
-- NEXUS TEAM MEMBERS
-- =====================================================

-- Generate UUIDs for NEXUS team members
DO $$
DECLARE
    eashwara_uuid UUID := uuid_generate_v4();
    yusuf_uuid UUID := uuid_generate_v4();
    darshan_uuid UUID := uuid_generate_v4();
    gaushik_uuid UUID := uuid_generate_v4();
    sakthivel_uuid UUID := uuid_generate_v4();
BEGIN
    -- Insert into auth.users first
    INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, aud, role) VALUES
    (eashwara_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (yusuf_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (darshan_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (gaushik_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (sakthivel_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated');

    -- Insert users first
    INSERT INTO users (id, email, full_name) VALUES
    (eashwara_uuid, '<EMAIL>', 'Eashwara Prasadh'),
    (yusuf_uuid, '<EMAIL>', 'Yusuf Fayas'),
    (darshan_uuid, '<EMAIL>', 'Darshan K'),
    (gaushik_uuid, '<EMAIL>', 'Gaushik'),
    (sakthivel_uuid, '<EMAIL>', 'Sakthivel');

    -- Eashwara Prasadh (Service Delivery Manager, Leadership) - SUPERADMIN
    PERFORM create_employee_with_role_and_badges(
        eashwara_uuid,
        'EMP011',
        'Eashwara',
        'Prasadh',
        '<EMAIL>',
        'Service Delivery Manager',
        'NEXUS',
        'Superadmin', -- Designated as Superadmin for system administration
        ARRAY['Service Delivery Manager', 'Leadership']
    );

    -- Yusuf Fayas (Technical Account Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        yusuf_uuid,
        'EMP012',
        'Yusuf',
        'Fayas',
        '<EMAIL>',
        'Technical Account Manager',
        'NEXUS',
        'Admin',
        ARRAY['Technical Account Manager', 'Associate Trainee', 'Leadership']
    );

    -- Darshan K (Client Experience Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        darshan_uuid,
        'EMP013',
        'Darshan',
        'K',
        '<EMAIL>',
        'Client Experience Manager',
        'NEXUS',
        'Admin',
        ARRAY['Client Experience Manager', 'Associate Trainee', 'Leadership']
    );

    -- Gaushik (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        gaushik_uuid,
        'EMP014',
        'Gaushik',
        '',
        '<EMAIL>',
        'Associate Trainee',
        'NEXUS',
        'User',
        ARRAY['Associate Trainee']
    );

    -- Sakthivel (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        sakthivel_uuid,
        'EMP015',
        'Sakthivel',
        '',
        '<EMAIL>',
        'Associate Trainee',
        'NEXUS',
        'User',
        ARRAY['Associate Trainee']
    );
END $$;

-- =====================================================
-- TITAN TEAM MEMBERS
-- =====================================================

-- Generate UUIDs for TITAN team members
DO $$
DECLARE
    aamina_uuid UUID := uuid_generate_v4();
    gowtham_uuid UUID := uuid_generate_v4();
    prasanna_uuid UUID := uuid_generate_v4();
    yamini_uuid UUID := uuid_generate_v4();
    shrimathi_uuid UUID := uuid_generate_v4();
BEGIN
    -- Insert into auth.users first
    INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, aud, role) VALUES
    (aamina_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (gowtham_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (prasanna_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (yamini_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated'),
    (shrimathi_uuid, '<EMAIL>', NOW(), NOW(), NOW(), 'authenticated', 'authenticated');

    -- Insert users first
    INSERT INTO users (id, email, full_name) VALUES
    (aamina_uuid, '<EMAIL>', 'Aamina Begam T'),
    (gowtham_uuid, '<EMAIL>', 'Gowtham Kollati'),
    (prasanna_uuid, '<EMAIL>', 'Prasanna'),
    (yamini_uuid, '<EMAIL>', 'Yamini'),
    (shrimathi_uuid, '<EMAIL>', 'Shri Mathi');

    -- Aamina Begam T (Service Delivery Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        aamina_uuid,
        'EMP016',
        'Aamina Begam',
        'T',
        '<EMAIL>',
        'Service Delivery Manager',
        'TITAN',
        'Admin',
        ARRAY['Service Delivery Manager', 'Associate Trainee', 'Leadership']
    );

    -- Gowtham Kollati (Technical Account Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        gowtham_uuid,
        'EMP017',
        'Gowtham',
        'Kollati',
        '<EMAIL>',
        'Technical Account Manager',
        'TITAN',
        'Admin',
        ARRAY['Technical Account Manager', 'Associate Trainee', 'Leadership']
    );

    -- Prasanna (Client Experience Manager, Associate Trainee, Leadership)
    PERFORM create_employee_with_role_and_badges(
        prasanna_uuid,
        'EMP018',
        'Prasanna',
        '',
        '<EMAIL>',
        'Client Experience Manager',
        'TITAN',
        'Admin',
        ARRAY['Client Experience Manager', 'Associate Trainee', 'Leadership']
    );

    -- Yamini (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        yamini_uuid,
        'EMP019',
        'Yamini',
        '',
        '<EMAIL>',
        'Associate Trainee',
        'TITAN',
        'User',
        ARRAY['Associate Trainee']
    );

    -- Shri Mathi (Associate Trainee)
    PERFORM create_employee_with_role_and_badges(
        shrimathi_uuid,
        'EMP020',
        'Shri',
        'Mathi',
        '<EMAIL>',
        'Associate Trainee',
        'TITAN',
        'User',
        ARRAY['Associate Trainee']
    );
END $$;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify all team members were created successfully
SELECT
    t.name as team_name,
    e.employee_id,
    e.first_name,
    e.last_name,
    e.email,
    e.designation,
    sr.name as role_name,
    sr.level as role_level
FROM employees e
JOIN teams t ON e.team_id = t.id
JOIN user_role_assignments ura ON e.user_id = ura.user_id
JOIN system_roles sr ON ura.system_role_id = sr.id
WHERE ura.is_active = true
ORDER BY t.name, e.employee_id;

-- Verify badge assignments
SELECT
    e.first_name || ' ' || e.last_name as employee_name,
    e.employee_id,
    t.name as team_name,
    b.name as badge_name,
    eb.assigned_date,
    eb.expiry_date,
    eb.status
FROM employee_badges eb
JOIN employees e ON eb.employee_id = e.id
JOIN teams t ON e.team_id = t.id
JOIN badges b ON eb.badge_id = b.id
ORDER BY t.name, e.employee_id, b.name;

-- Summary by team and role
SELECT
    t.name as team_name,
    sr.name as role_name,
    COUNT(*) as member_count
FROM employees e
JOIN teams t ON e.team_id = t.id
JOIN user_role_assignments ura ON e.user_id = ura.user_id
JOIN system_roles sr ON ura.system_role_id = sr.id
WHERE ura.is_active = true
GROUP BY t.name, sr.name, sr.level
ORDER BY t.name, sr.level;
