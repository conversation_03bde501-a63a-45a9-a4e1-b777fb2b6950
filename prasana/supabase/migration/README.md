# 🚀 Comprehensive Multi-Purpose Application Database Setup

This directory contains the complete database schema and migration files for a comprehensive multi-purpose application with HRMS functionality and role-based access control.

## 📋 Overview

The system implements a three-tier role-based access control system:
- **👤 User Role**: Basic access level
- **👨‍💼 Admin Role**: Elevated permissions for team management
- **🔑 Superadmin Role**: Full system access

## 🗂️ Migration Files

Execute these files in order in your Supabase SQL Editor:

### 1. `01_comprehensive_schema.sql`
- Creates all core tables for the application
- Includes HRMS, project management, time tracking, badges, and more
- Sets up proper indexes for performance
- **Tables Created**: 20+ tables including users, employees, projects, time_entries, badges, etc.

### 2. `02_rls_policies.sql`
- Implements Row Level Security (RLS) policies
- Enforces role-based access control at the database level
- Ensures data security and proper access restrictions
- **Policies Created**: 60+ RLS policies covering all tables

### 3. `03_initial_data.sql`
- Sets up system roles (User, Admin, Superadmin)
- Creates teams (ATHENA, DYNAMIX, NEXUS, TITAN)
- Initializes leave types, holidays, badges, and system settings
- **Data Inserted**: System configuration and reference data

### 4. `04_team_members_setup.sql`
- Provides helper functions for creating employees
- Contains commented scripts for all team members
- **Function Created**: `create_employee_with_role_and_badges()`

### 5. `05_triggers_and_functions.sql`
- Creates automation triggers and helper functions
- Sets up notification system and audit logging
- **Features**: Auto-notifications, leave balance updates, audit trails

## 🎯 Role-Based Permissions

### 👤 USER ROLE PERMISSIONS
- ✅ View all team members' timesheet entries and time logs
- ✅ View all projects they have access to
- ✅ Add and edit their own timesheet entries
- ✅ View their assigned badges/certifications and expiry dates
- ✅ Create and update their personal profile in HRMS
- ✅ View their own attendance, leave balance, and documents
- ✅ Apply for leaves and view leave history
- ✅ Access their own performance reviews and goals

### 👨‍💼 ADMIN ROLE PERMISSIONS
- ✅ All USER role permissions, plus:
- ✅ Create, edit, and manage projects
- ✅ Add and remove team members from projects
- ✅ Assign project roles and responsibilities
- ✅ Create and assign badges/certifications to employees
- ✅ Set badge expiry dates and manage renewals
- ✅ View and manage team timesheets (approve/reject)
- ✅ Access team-level reports and analytics
- ✅ Manage project milestones and deadlines
- ✅ View team performance metrics and progress

### 🔑 SUPERADMIN ROLE PERMISSIONS
- ✅ All USER and ADMIN role permissions, plus:
- ✅ Approve or reject leave applications for all employees
- ✅ Process payroll for all employees
- ✅ Assign and modify user roles (User, Admin, Superadmin)
- ✅ Manage system-wide settings and configurations
- ✅ Access all employee data across the organization
- ✅ Generate company-wide reports and analytics
- ✅ Manage organizational structure and hierarchies
- ✅ Configure leave types, holiday calendars, and policies
- ✅ Perform system administration tasks

## 🚀 Installation Steps

### Step 1: Execute Migration Files
Run each SQL file in your Supabase SQL Editor in the specified order:

```sql
-- 1. Execute 01_comprehensive_schema.sql
-- 2. Execute 02_rls_policies.sql  
-- 3. Execute 03_initial_data.sql
-- 4. Execute 04_team_members_setup.sql
-- 5. Execute 05_triggers_and_functions.sql
```

### Step 2: Verify Installation
After running all migrations, verify the setup:

```sql
-- Check if all tables are created
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check if RLS is enabled
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- Check system roles
SELECT * FROM system_roles ORDER BY level;

-- Check teams
SELECT * FROM teams;
```

### Step 3: Create Team Members
After users register through Supabase Auth, create their employee records:

```sql
-- Example for creating an employee (replace USER_ID_FROM_AUTH with actual auth.users.id)
SELECT create_employee_with_role_and_badges(
    'USER_ID_FROM_AUTH',
    'EMP001',
    'Sri',
    'Ram',
    '<EMAIL>',
    'Service Delivery Manager',
    'ATHENA',
    'Admin',
    ARRAY['Service Delivery Manager', 'Associate Trainee', 'Leadership']
);
```

## 👥 Team Structure

### ATHENA Team
- Sri Ram (Service Delivery Manager, Admin)
- Selvendrane (Technical Account Manager, Admin)
- Maheshwaran (Client Experience Manager, Admin)
- Theepatharan (Associate Trainee, User)
- Fazeela (Associate Trainee, User)
- Sivaranjani (Associate Trainee, User)

### DYNAMIX Team
- Yuvaraj (Service Delivery Manager, Admin)
- Purushoth (Technical Account Manager, Admin)
- Kiyshore K (Client Experience Manager, Admin)
- Praveen Dommeti (Associate Trainee, User)

### NEXUS Team
- Eashwara Prasadh (Service Delivery Manager, **Superadmin**)
- Yusuf Fayas (Technical Account Manager, Admin)
- Darshan K (Client Experience Manager, Admin)
- Gaushik (Associate Trainee, User)
- Sakthivel (Associate Trainee, User)

### TITAN Team
- Aamina Begam T (Service Delivery Manager, Admin)
- Gowtham Kollati (Technical Account Manager, Admin)
- Prasanna (Client Experience Manager, Admin)
- Yamini (Associate Trainee, User)
- Shri Mathi (Associate Trainee, User)

## 🔧 Key Features

### 📊 Project Management
- Complete project lifecycle management
- Team member assignments and role management
- Project milestones and deadline tracking
- Resource allocation and utilization

### ⏰ Time Tracking & Timesheets
- Individual and team timesheet management
- Approval workflows for timesheet entries
- Project-based time allocation
- Billable vs non-billable hour tracking

### 🏆 Badges & Certifications
- Badge assignment and management
- Expiry date tracking and notifications
- Role-based badge assignment permissions
- Certification renewal management

### 🏢 HRMS Functionality
- Complete employee lifecycle management
- Leave management with approval workflows
- Attendance tracking and reporting
- Performance review and goal management
- Payroll processing and management
- Document management with access controls

### 🔔 Notification System
- Real-time notifications for approvals
- Badge expiry reminders
- Leave status updates
- Timesheet approval notifications

### 🔍 Audit & Security
- Comprehensive audit logging
- Row Level Security (RLS) implementation
- Role-based data access control
- Change tracking and history

## 🛠️ Maintenance

### Regular Tasks
- Monitor badge expiry dates
- Review and approve timesheets
- Process leave applications
- Update employee information
- Generate reports and analytics

### Scheduled Functions
Consider setting up cron jobs for:
- Badge expiry notifications (daily)
- Leave balance calculations (monthly)
- Performance review reminders (quarterly)
- System cleanup tasks (weekly)

## 📞 Support

For issues or questions:
1. Check the RLS policies if access is denied
2. Verify user role assignments
3. Ensure proper team assignments
4. Review audit logs for changes

## 🔄 Updates

To add new features:
1. Create new migration files with incremental numbers
2. Update RLS policies as needed
3. Add appropriate triggers and functions
4. Update documentation

---

**Note**: This system is designed to be comprehensive and scalable. All role-based permissions are enforced at the database level through RLS policies, ensuring data security and proper access control.
