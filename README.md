# INTERNAL - Comprehensive HRMS System

## Overview
INTERNAL is a comprehensive Human Resource Management System (HRMS) built with React, TypeScript, and Vite. It features a modern UI powered by Tailwind CSS with role-based access control and integrates with Supabase for data management. The application provides comprehensive tools for managing employees, projects, teams, payroll, and analytics.

## Features

### 1. Role-Based Access Control
- **User**: View team timesheets, manage personal data
- **Admin**: Manage projects, team assignments, employee management
- **Superadmin**: Full system access including payroll, user role management

### 2. Employee Management
- Comprehensive employee profiles with badges and skills
- Performance tracking and management
- Leave management system
- Document management
- Employee directory with organizational hierarchy

### 3. Team Directory & Organizational Chart
- 5-level organizational hierarchy (CEO → CRO → SDM → TDM/CXM → Team Members)
- Interactive organizational charts with department color coding
- Team analytics and reporting
- Advanced search and filtering capabilities

### 4. Payroll Management (Super Admin Only)
- Automated payroll processing with INR currency support
- Tax calculations and deductions
- Integration with attendance/timesheet systems
- Comprehensive payroll reports and audit trails

### 5. Project Management
- Multiple view modes (<PERSON>rid, <PERSON>, <PERSON><PERSON>ban)
- Project creation and editing with team assignments
- Status tracking and milestone management
- Client management and resource allocation

### 6. Time Tracking & Timesheets
- Comprehensive timesheet management
- Time tracking per project with approval workflows
- Resource utilization tracking
- Export functionality for reporting

### 7. Analytics Dashboard
- Real-time analytics with professional UI design
- Employee metrics and performance indicators
- Project analytics and team productivity metrics
- Financial reporting and payroll analytics

### 8. HR Dashboard
- Super Admin-only HR management interface
- Employee profile management with badges
- Leave balance tracking and approval
- Performance review management

## Tech Stack

### Frontend
- React 19.1.0
- TypeScript 5.8.3
- Vite 6.3.5
- Tailwind CSS 4.1.7
- Lucide React (for icons)
- Recharts (for analytics visualizations)
- date-fns (for date formatting)

### Backend
- Supabase (Database, Authentication, Storage)
- Row Level Security (RLS) policies for data protection
- Custom SQL triggers and functions for data synchronization

## Project Structure

```
prasana/
├── src/
│   ├── components/
│   │   ├── hrms/              # HRMS-specific components
│   │   ├── payroll/           # Payroll management components
│   │   ├── TeamDirectory/     # Team directory and org chart
│   │   ├── admin/             # Admin-only components
│   │   └── ...
│   ├── contexts/              # React context providers
│   ├── data/                  # Data management and API calls
│   ├── pages/                 # Main application pages
│   ├── services/              # Service layer for backend interactions
│   ├── types/                 # TypeScript type definitions
│   ├── hooks/                 # Custom React hooks
│   ├── utils/                 # Utility functions
│   └── styles/                # CSS and styling files
├── public/
│   ├── badges/                # Professional badge images
│   └── profiles/              # Employee profile pictures
├── supabase/
│   └── migration/             # Database migration files
└── docs/                      # Documentation files
```

## Setup Instructions

1. Clone the repository
2. Install dependencies:
   ```bash
   cd prasana
   npm install
   ```

3. Configure environment variables:
   - Create a `.env` file in the prasana directory
   - Add your Supabase configuration:
     ```
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```

4. Run database migrations:
   - Execute the SQL files in `supabase/migration/` in order
   - Set up RLS policies as documented

5. Start the development server:
   ```bash
   npm run dev
   ```

6. Build for production:
   ```bash
   npm run build
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## Key Features

### HRMS Dashboard
- Comprehensive employee management with role-based access
- Real-time analytics and reporting
- Professional badge system with 8 predefined badges
- Integration with Supabase auth.users for profile management

### Payroll System
- INR currency support with proper formatting
- Automated tax calculations and deductions
- Integration with timesheet data for accurate payroll processing
- Comprehensive audit trails and reporting

### Team Directory
- Interactive organizational charts with hierarchical structure
- Department color coding (NEXUS-Blue, DYNAMIX-Green, TITAN-Orange, ATHENA-Purple)
- Advanced search and filtering capabilities
- Team analytics and performance metrics

### Security
- Row Level Security (RLS) policies for data protection
- Role-based access control with three-tier system
- Secure authentication with Supabase Auth
- Audit logging for sensitive operations

## Organizational Structure

- **CEO**: ARUN G
- **CRO**: Priyadarshini
- **SDMs**: Eashwara Prasadh (NEXUS), Aamina Begam T (TITAN), Sri Ram (ATHENA), Yuvaraj (DYNAMIX)
- **TDMs/CXMs**: Technical and client-facing team leads
- **Team Members**: Individual contributors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is private and proprietary. All rights reserved.
